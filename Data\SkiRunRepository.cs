using System.Data.SQLite;
using SkiTrackApp.Models;

namespace SkiTrackApp.Data
{
    public class SkiRunRepository
    {
        public List<SkiRun> GetUserRuns(int userId)
        {
            var runs = new List<SkiRun>();
            
            using var connection = DatabaseManager.GetConnection();
            connection.Open();
            
            var command = new SQLiteCommand(@"
                SELECT sr.Id, sr.UserId, sr.ResortId, sr.Date, sr.Distance, sr.Duration, 
                       sr.AverageSpeed, sr.MaxSpeed, sr.ElevationChange, sr.Notes, sr.ImagePath,
                       r.Name as ResortName, r.Location as ResortLocation, r.State as ResortState
                FROM SkiRuns sr
                INNER JOIN Resorts r ON sr.ResortId = r.Id
                WHERE sr.UserId = @userId
                ORDER BY sr.Date DESC", connection);
            
            command.Parameters.AddWithValue("@userId", userId);
            
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var run = new SkiRun
                {
                    Id = reader.GetInt32(0), // sr.Id
                    UserId = reader.GetInt32(1), // sr.UserId
                    ResortId = reader.GetInt32(2), // sr.ResortId
                    Date = reader.GetDateTime(3), // sr.Date
                    Distance = reader.GetDouble(4), // sr.Distance
                    Duration = TimeSpan.Parse(reader.GetString(5)), // sr.Duration
                    AverageSpeed = reader.GetDouble(6), // sr.AverageSpeed
                    MaxSpeed = reader.GetDouble(7), // sr.MaxSpeed
                    ElevationChange = reader.GetDouble(8), // sr.ElevationChange
                    Notes = reader.IsDBNull(9) ? "" : reader.GetString(9), // sr.Notes
                    ImagePath = reader.IsDBNull(10) ? "" : reader.GetString(10), // sr.ImagePath
                    Resort = new Resort
                    {
                        Id = reader.GetInt32(2), // sr.ResortId
                        Name = reader.GetString(11), // r.Name as ResortName
                        Location = reader.GetString(12), // r.Location as ResortLocation
                        State = reader.GetString(13), // r.State as ResortState
                        Country = "",
                        TotalAcres = 0,
                        TotalTrails = 0,
                        ImageUrl = "",
                        Description = "",
                        IsPopular = false
                    }
                };
                
                runs.Add(run);
            }
            
            return runs;
        }
        
        public RunStatistics GetUserStatistics(int userId)
        {
            using var connection = DatabaseManager.GetConnection();
            connection.Open();
            
            var command = new SQLiteCommand(@"
                SELECT 
                    COUNT(*) as TotalRuns,
                    COALESCE(SUM(Distance), 0) as TotalDistance,
                    COALESCE(AVG(AverageSpeed), 0) as AverageSpeed,
                    COALESCE(MAX(MaxSpeed), 0) as MaxSpeed,
                    COALESCE(SUM(ElevationChange), 0) as TotalElevation,
                    COALESCE(SUM(
                        CAST(substr(Duration, 1, instr(Duration, ':') - 1) AS INTEGER) * 3600 +
                        CAST(substr(Duration, instr(Duration, ':') + 1, 2) AS INTEGER) * 60 +
                        CAST(substr(Duration, instr(Duration, ':', instr(Duration, ':') + 1) + 1) AS INTEGER)
                    ), 0) as TotalSeconds
                FROM SkiRuns 
                WHERE UserId = @userId", connection);
            
            command.Parameters.AddWithValue("@userId", userId);
            
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new RunStatistics
                {
                    TotalRuns = reader.GetInt32(0), // TotalRuns
                    TotalDistance = reader.GetDouble(1), // TotalDistance
                    AverageSpeed = reader.GetDouble(2), // AverageSpeed
                    MaxSpeed = reader.GetDouble(3), // MaxSpeed
                    TotalElevation = reader.GetDouble(4), // TotalElevation
                    TotalTime = TimeSpan.FromSeconds(reader.GetDouble(5)) // TotalSeconds
                };
            }
            
            return new RunStatistics();
        }
        
        public bool AddRun(SkiRun run)
        {
            using var connection = DatabaseManager.GetConnection();
            connection.Open();
            
            var command = new SQLiteCommand(@"
                INSERT INTO SkiRuns (UserId, ResortId, Date, Distance, Duration, AverageSpeed, MaxSpeed, ElevationChange, Notes, ImagePath)
                VALUES (@userId, @resortId, @date, @distance, @duration, @averageSpeed, @maxSpeed, @elevationChange, @notes, @imagePath)", connection);
            
            command.Parameters.AddWithValue("@userId", run.UserId);
            command.Parameters.AddWithValue("@resortId", run.ResortId);
            command.Parameters.AddWithValue("@date", run.Date);
            command.Parameters.AddWithValue("@distance", run.Distance);
            command.Parameters.AddWithValue("@duration", run.Duration.ToString());
            command.Parameters.AddWithValue("@averageSpeed", run.AverageSpeed);
            command.Parameters.AddWithValue("@maxSpeed", run.MaxSpeed);
            command.Parameters.AddWithValue("@elevationChange", run.ElevationChange);
            command.Parameters.AddWithValue("@notes", run.Notes);
            command.Parameters.AddWithValue("@imagePath", run.ImagePath);
            
            return command.ExecuteNonQuery() > 0;
        }
        
        public SkiRun? GetRunById(int runId)
        {
            using var connection = DatabaseManager.GetConnection();
            connection.Open();
            
            var command = new SQLiteCommand(@"
                SELECT sr.Id, sr.UserId, sr.ResortId, sr.Date, sr.Distance, sr.Duration, 
                       sr.AverageSpeed, sr.MaxSpeed, sr.ElevationChange, sr.Notes, sr.ImagePath,
                       r.Name as ResortName, r.Location as ResortLocation, r.State as ResortState
                FROM SkiRuns sr
                INNER JOIN Resorts r ON sr.ResortId = r.Id
                WHERE sr.Id = @runId", connection);
            
            command.Parameters.AddWithValue("@runId", runId);
            
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SkiRun
                {
                    Id = reader.GetInt32(0), // sr.Id
                    UserId = reader.GetInt32(1), // sr.UserId
                    ResortId = reader.GetInt32(2), // sr.ResortId
                    Date = reader.GetDateTime(3), // sr.Date
                    Distance = reader.GetDouble(4), // sr.Distance
                    Duration = TimeSpan.Parse(reader.GetString(5)), // sr.Duration
                    AverageSpeed = reader.GetDouble(6), // sr.AverageSpeed
                    MaxSpeed = reader.GetDouble(7), // sr.MaxSpeed
                    ElevationChange = reader.GetDouble(8), // sr.ElevationChange
                    Notes = reader.IsDBNull(9) ? "" : reader.GetString(9), // sr.Notes
                    ImagePath = reader.IsDBNull(10) ? "" : reader.GetString(10), // sr.ImagePath
                    Resort = new Resort
                    {
                        Id = reader.GetInt32(2), // sr.ResortId
                        Name = reader.GetString(11), // r.Name as ResortName
                        Location = reader.GetString(12), // r.Location as ResortLocation
                        State = reader.GetString(13), // r.State as ResortState
                        Country = "",
                        TotalAcres = 0,
                        TotalTrails = 0,
                        ImageUrl = "",
                        Description = "",
                        IsPopular = false
                    }
                };
            }
            
            return null;
        }
    }
}
