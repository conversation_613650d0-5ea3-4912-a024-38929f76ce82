using System.Data.SQLite;
using System.Security.Cryptography;
using System.Text;
using SkiTrackApp.Models;

namespace SkiTrackApp.Data
{
    public class UserRepository
    {
        public User? AuthenticateUser(string usernameOrEmail, string password)
        {
            using var connection = DatabaseManager.GetConnection();
            
            var command = new SQLiteCommand(@"
                SELECT Id, Username, Email, PasswordHash, FirstName, LastName, ProfileImagePath, CreatedAt, LastLoginAt
                FROM Users 
                WHERE (Username = @usernameOrEmail OR Email = @usernameOrEmail)", connection);
            
            command.Parameters.AddWithValue("@usernameOrEmail", usernameOrEmail);
            
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                var storedHash = reader.GetString(3); // PasswordHash
                if (VerifyPassword(password, storedHash))
                {
                    var user = new User
                    {
                        Id = reader.GetInt32(0), // Id
                        Username = reader.GetString(1), // Username
                        Email = reader.GetString(2), // Email
                        PasswordHash = storedHash,
                        FirstName = reader.IsDBNull(4) ? "" : reader.GetString(4), // FirstName
                        LastName = reader.IsDBNull(5) ? "" : reader.GetString(5), // LastName
                        ProfileImagePath = reader.IsDBNull(6) ? "" : reader.GetString(6), // ProfileImagePath
                        CreatedAt = reader.GetDateTime(7), // CreatedAt
                        LastLoginAt = reader.IsDBNull(8) ? DateTime.MinValue : reader.GetDateTime(8) // LastLoginAt
                    };
                    
                    // Update last login time
                    UpdateLastLogin(user.Id);
                    
                    return user;
                }
            }
            
            return null;
        }
        
        public bool CreateUser(string username, string email, string password, string firstName = "", string lastName = "")
        {
            using var connection = DatabaseManager.GetConnection();
            
            // Check if username or email already exists
            var checkCommand = new SQLiteCommand(@"
                SELECT COUNT(*) FROM Users WHERE Username = @username OR Email = @email", connection);
            checkCommand.Parameters.AddWithValue("@username", username);
            checkCommand.Parameters.AddWithValue("@email", email);
            
            var count = Convert.ToInt32(checkCommand.ExecuteScalar());
            if (count > 0) return false; // User already exists
            
            var passwordHash = HashPassword(password);
            
            var insertCommand = new SQLiteCommand(@"
                INSERT INTO Users (Username, Email, PasswordHash, FirstName, LastName, CreatedAt)
                VALUES (@username, @email, @passwordHash, @firstName, @lastName, @createdAt)", connection);
            
            insertCommand.Parameters.AddWithValue("@username", username);
            insertCommand.Parameters.AddWithValue("@email", email);
            insertCommand.Parameters.AddWithValue("@passwordHash", passwordHash);
            insertCommand.Parameters.AddWithValue("@firstName", firstName);
            insertCommand.Parameters.AddWithValue("@lastName", lastName);
            insertCommand.Parameters.AddWithValue("@createdAt", DateTime.Now);
            
            return insertCommand.ExecuteNonQuery() > 0;
        }
        
        private void UpdateLastLogin(int userId)
        {
            using var connection = DatabaseManager.GetConnection();
            
            var command = new SQLiteCommand(@"
                UPDATE Users SET LastLoginAt = @lastLogin WHERE Id = @userId", connection);
            command.Parameters.AddWithValue("@lastLogin", DateTime.Now);
            command.Parameters.AddWithValue("@userId", userId);
            
            command.ExecuteNonQuery();
        }
        
        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SkiTrackSalt"));
            return Convert.ToBase64String(hashedBytes);
        }
        
        private bool VerifyPassword(string password, string hash)
        {
            return HashPassword(password) == hash;
        }
    }
}
