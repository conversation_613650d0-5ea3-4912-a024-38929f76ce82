using System.Data.SQLite;
using SkiTrackApp.Models;

namespace SkiTrackApp.Data
{
    public class ResortRepository
    {
        public List<Resort> GetAllResorts()
        {
            var resorts = new List<Resort>();

            using var connection = DatabaseManager.GetConnection();
            connection.Open();

            var command = new SQLiteCommand(@"
                SELECT Id, Name, Location, State, Country, TotalAcres, TotalTrails, ImageUrl, Description, IsPopular
                FROM Resorts
                ORDER BY IsPopular DESC, Name", connection);

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                resorts.Add(CreateResortFromReader(reader));
            }

            return resorts;
        }

        private Resort CreateResortFromReader(SQLiteDataReader reader)
        {
            return new Resort
            {
                Id = reader.GetInt32(0), // Id
                Name = reader.GetString(1), // Name
                Location = reader.GetString(2), // Location
                State = reader.GetString(3), // State
                Country = reader.GetString(4), // Country
                TotalAcres = reader.GetInt32(5), // TotalAcres
                TotalTrails = reader.GetInt32(6), // TotalTrails
                ImageUrl = reader.IsDBNull(7) ? "" : reader.GetString(7), // ImageUrl
                Description = reader.IsDBNull(8) ? "" : reader.GetString(8), // Description
                IsPopular = reader.GetBoolean(9) // IsPopular
            };
        }
        
        public List<Resort> GetPopularResorts()
        {
            var resorts = new List<Resort>();

            using var connection = DatabaseManager.GetConnection();
            connection.Open();

            var command = new SQLiteCommand(@"
                SELECT Id, Name, Location, State, Country, TotalAcres, TotalTrails, ImageUrl, Description, IsPopular
                FROM Resorts
                WHERE IsPopular = 1
                ORDER BY Name", connection);

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                resorts.Add(CreateResortFromReader(reader));
            }

            return resorts;
        }
        
        public Resort? GetResortById(int id)
        {
            using var connection = DatabaseManager.GetConnection();
            connection.Open();

            var command = new SQLiteCommand(@"
                SELECT Id, Name, Location, State, Country, TotalAcres, TotalTrails, ImageUrl, Description, IsPopular
                FROM Resorts
                WHERE Id = @id", connection);

            command.Parameters.AddWithValue("@id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return CreateResortFromReader(reader);
            }

            return null;
        }
        
        public List<Resort> SearchResorts(string searchTerm)
        {
            var resorts = new List<Resort>();

            using var connection = DatabaseManager.GetConnection();
            connection.Open();

            var command = new SQLiteCommand(@"
                SELECT Id, Name, Location, State, Country, TotalAcres, TotalTrails, ImageUrl, Description, IsPopular
                FROM Resorts
                WHERE Name LIKE @searchTerm OR Location LIKE @searchTerm OR State LIKE @searchTerm
                ORDER BY IsPopular DESC, Name", connection);

            command.Parameters.AddWithValue("@searchTerm", $"%{searchTerm}%");

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                resorts.Add(CreateResortFromReader(reader));
            }

            return resorts;
        }
    }
}
