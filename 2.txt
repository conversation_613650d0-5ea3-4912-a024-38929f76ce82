<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-gray-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="@container">
          <div class="@[480px]:px-4 @[480px]:py-3">
            <div
              class="w-full bg-center bg-no-repeat bg-cover flex flex-col justify-end overflow-hidden bg-gray-50 @[480px]:rounded-xl min-h-80"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBuzgdRTCkzdhrtqzb1A1RkOFfBMm70jf_FWL39fvWYiSV5LafWPVeh8SUlpDycv4XzLwVmSTi32xNSAPeNKWH07rl4kvMNTI5aOSwuiNsGPqHz5wbAGGmGESwgKdSMPYFPPdT8xtWVC9_9-4a3f4zGxsCHCKWrxqpx63ekM92R5bahgFUbDu0RdGuly1GCBH6iqn2Up6CzNOzawd9evPg1yMCg-A4-j8e-CDvCQcBUs__79HSikT4qJmnCq9OJu3oAi1dpyC9HtJc");'
            ></div>
          </div>
        </div>
        <h1 class="text-[#101419] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 text-center pb-3 pt-5">Welcome to SkiTrack</h1>
        <div class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label class="flex flex-col min-w-40 flex-1">
            <input
              placeholder="Email or Username"
              class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#101419] focus:outline-0 focus:ring-0 border-none bg-[#e9edf1] focus:border-none h-14 placeholder:text-[#57738e] p-4 text-base font-normal leading-normal"
              value=""
            />
          </label>
        </div>
        <div class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label class="flex flex-col min-w-40 flex-1">
            <input
              placeholder="Password"
              class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#101419] focus:outline-0 focus:ring-0 border-none bg-[#e9edf1] focus:border-none h-14 placeholder:text-[#57738e] p-4 text-base font-normal leading-normal"
              value=""
            />
          </label>
        </div>
        <p class="text-[#57738e] text-sm font-normal leading-normal pb-3 pt-1 px-4 text-center underline">Forgot Password?</p>
        <div class="flex justify-center">
          <div class="flex flex-1 gap-3 max-w-[480px] flex-col items-stretch px-4 py-3">
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 bg-[#327fcc] text-gray-50 text-base font-bold leading-normal tracking-[0.015em] w-full"
            >
              <span class="truncate">Log In</span>
            </button>
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 bg-[#e9edf1] text-[#101419] text-base font-bold leading-normal tracking-[0.015em] w-full"
            >
              <span class="truncate">Sign Up</span>
            </button>
          </div>
        </div>
      </div>
      <div>
        <div
          class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-none group-[:not(.dark)]/design-root:hidden"
          style='background-image: url("/dark.svg"); aspect-ratio: 390 / 320;'
        ></div>
        <div
          class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-none group-[.dark]/design-root:hidden"
          style='background-image: url("/light.svg"); aspect-ratio: 390 / 320;'
        ></div>
      </div>
    </div>
  </body>
</html>
