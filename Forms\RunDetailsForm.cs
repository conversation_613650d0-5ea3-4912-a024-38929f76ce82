using SkiTrackApp.Data;
using SkiTrackApp.Models;

namespace SkiTrackApp.Forms
{
    public partial class RunDetailsForm : Form
    {
        private readonly SkiRunRepository skiRunRepository;
        private readonly int runId;
        private SkiRun? currentRun;
        
        public RunDetailsForm(int runId)
        {
            InitializeComponent();
            this.runId = runId;
            skiRunRepository = new SkiRunRepository();
            SetupForm();
            LoadRunDetails();
        }
        
        private void SetupForm()
        {
            this.Text = "Run Details";
            this.Size = new Size(450, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(249, 250, 251);
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            
            // Header
            var headerLabel = new Label
            {
                Text = "Run Details",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Chart placeholder
            var chartPanel = new Panel
            {
                Size = new Size(400, 200),
                Location = new Point(20, 80),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var chartLabel = new Label
            {
                Text = "📊 Speed Chart\n(Chart visualization would go here)",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(107, 114, 128),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            chartPanel.Controls.Add(chartLabel);
            
            // Details panel
            var detailsPanel = new Panel
            {
                Size = new Size(400, 300),
                Location = new Point(20, 300),
                BackColor = Color.Transparent
            };
            
            LoadRunDetailsIntoPanel(detailsPanel);
            
            // Close button
            var closeButton = new Button
            {
                Text = "Close",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(400, 45),
                Location = new Point(20, 620),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(16, 20, 25),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();
            
            // Add hover effect
            closeButton.MouseEnter += (s, e) => closeButton.BackColor = Color.FromArgb(209, 213, 219);
            closeButton.MouseLeave += (s, e) => closeButton.BackColor = Color.FromArgb(233, 237, 241);
            
            mainPanel.Controls.AddRange(new Control[] { headerLabel, chartPanel, detailsPanel, closeButton });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoadRunDetails()
        {
            currentRun = skiRunRepository.GetRunById(runId);
            
            if (currentRun == null)
            {
                MessageBox.Show("Run not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
                return;
            }
            
            this.Text = $"Run Details - {currentRun.FormattedDate}";
        }
        
        private void LoadRunDetailsIntoPanel(Panel detailsPanel)
        {
            if (currentRun == null) return;
            
            detailsPanel.Controls.Clear();
            
            int yPos = 0;
            
            // Date and Resort
            var dateResortLabel = new Label
            {
                Text = $"{currentRun.FormattedDate} at {currentRun.Resort?.Name ?? "Unknown Resort"}",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(0, yPos),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            yPos += 50;
            
            // Statistics grid
            var stats = new[]
            {
                ("Distance", $"{currentRun.Distance:F1} km"),
                ("Duration", currentRun.FormattedDuration),
                ("Average Speed", $"{currentRun.AverageSpeed:F1} km/h"),
                ("Max Speed", $"{currentRun.MaxSpeed:F1} km/h"),
                ("Elevation Change", $"{currentRun.ElevationChange:F0} m"),
                ("Resort Location", currentRun.Resort?.FullLocation ?? "Unknown")
            };
            
            for (int i = 0; i < stats.Length; i++)
            {
                var row = i / 2;
                var col = i % 2;
                
                var statPanel = CreateStatPanel(stats[i].Item1, stats[i].Item2, 
                    col * 200, yPos + row * 60);
                detailsPanel.Controls.Add(statPanel);
            }
            
            yPos += 180;
            
            // Notes section
            if (!string.IsNullOrWhiteSpace(currentRun.Notes))
            {
                var notesTitle = new Label
                {
                    Text = "Notes",
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.FromArgb(16, 20, 25),
                    Size = new Size(400, 25),
                    Location = new Point(0, yPos)
                };
                
                var notesLabel = new Label
                {
                    Text = currentRun.Notes,
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.FromArgb(87, 115, 142),
                    Size = new Size(400, 60),
                    Location = new Point(0, yPos + 30),
                    BackColor = Color.White,
                    BorderStyle = BorderStyle.FixedSingle,
                    Padding = new Padding(10)
                };
                
                detailsPanel.Controls.AddRange(new Control[] { notesTitle, notesLabel });
            }
            
            detailsPanel.Controls.Add(dateResortLabel);
        }
        
        private Panel CreateStatPanel(string title, string value, int x, int y)
        {
            var panel = new Panel
            {
                Size = new Size(180, 50),
                Location = new Point(x, y),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(170, 20),
                Location = new Point(5, 5)
            };
            
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(170, 25),
                Location = new Point(5, 20)
            };
            
            panel.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            
            return panel;
        }
    }
}
