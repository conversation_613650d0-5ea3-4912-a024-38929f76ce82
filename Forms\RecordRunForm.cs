using SkiTrackApp.Data;
using SkiTrackApp.Models;
using SkiTrackApp.Services;

namespace SkiTrackApp.Forms
{
    public partial class RecordRunForm : Form
    {
        private readonly SkiRunRepository skiRunRepository;
        private readonly ResortRepository resortRepository;
        private ComboBox? resortComboBox;
        private DateTimePicker? datePicker;
        private NumericUpDown? distanceNumeric;
        private NumericUpDown? hoursNumeric;
        private NumericUpDown? minutesNumeric;
        private NumericUpDown? avgSpeedNumeric;
        private NumericUpDown? maxSpeedNumeric;
        private NumericUpDown? elevationNumeric;
        private TextBox? notesTextBox;
        private Button? saveButton;
        private Button? cancelButton;
        
        private int? preselectedResortId;
        
        public RecordRunForm(int? resortId = null)
        {
            InitializeComponent();
            skiRunRepository = new SkiRunRepository();
            resortRepository = new ResortRepository();
            preselectedResortId = resortId;
            SetupForm();
            LoadResorts();
        }
        
        private void SetupForm()
        {
            this.Text = "Record New Run";
            this.Size = new Size(450, 650);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(249, 250, 251);
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            
            // Title
            var titleLabel = new Label
            {
                Text = "Record New Run",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            int yPos = 80;
            
            // Resort selection
            var resortLabel = new Label
            {
                Text = "Resort",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, yPos)
            };
            
            resortComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            
            yPos += 80;
            
            // Date
            var dateLabel = new Label
            {
                Text = "Date",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, yPos)
            };
            
            datePicker = new DateTimePicker
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 35),
                Location = new Point(20, yPos + 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };
            
            yPos += 80;
            
            // Distance
            var distanceLabel = new Label
            {
                Text = "Distance (km)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, yPos)
            };
            
            distanceNumeric = new NumericUpDown
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 35),
                Location = new Point(20, yPos + 25),
                Minimum = 0.1m,
                Maximum = 100m,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Value = 1.0m
            };
            
            yPos += 80;
            
            // Duration
            var durationLabel = new Label
            {
                Text = "Duration",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, yPos)
            };
            
            var durationPanel = new Panel
            {
                Size = new Size(400, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.Transparent
            };
            
            hoursNumeric = new NumericUpDown
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(80, 35),
                Location = new Point(0, 0),
                Minimum = 0,
                Maximum = 12,
                Value = 1
            };
            
            var hoursLabel = new Label
            {
                Text = "hours",
                Font = new Font("Segoe UI", 10),
                Size = new Size(50, 35),
                Location = new Point(90, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            minutesNumeric = new NumericUpDown
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(80, 35),
                Location = new Point(160, 0),
                Minimum = 0,
                Maximum = 59,
                Value = 30
            };
            
            var minutesLabel = new Label
            {
                Text = "minutes",
                Font = new Font("Segoe UI", 10),
                Size = new Size(60, 35),
                Location = new Point(250, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            durationPanel.Controls.AddRange(new Control[] { hoursNumeric, hoursLabel, minutesNumeric, minutesLabel });
            
            yPos += 80;
            
            // Average Speed
            var avgSpeedLabel = new Label
            {
                Text = "Average Speed (km/h)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(190, 20),
                Location = new Point(20, yPos)
            };
            
            avgSpeedNumeric = new NumericUpDown
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(190, 35),
                Location = new Point(20, yPos + 25),
                Minimum = 1m,
                Maximum = 200m,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Value = 25.0m
            };
            
            // Max Speed
            var maxSpeedLabel = new Label
            {
                Text = "Max Speed (km/h)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(190, 20),
                Location = new Point(230, yPos)
            };
            
            maxSpeedNumeric = new NumericUpDown
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(190, 35),
                Location = new Point(230, yPos + 25),
                Minimum = 1m,
                Maximum = 300m,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Value = 45.0m
            };
            
            yPos += 80;
            
            // Elevation Change
            var elevationLabel = new Label
            {
                Text = "Elevation Change (m)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, yPos)
            };
            
            elevationNumeric = new NumericUpDown
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 35),
                Location = new Point(20, yPos + 25),
                Minimum = 0m,
                Maximum = 5000m,
                DecimalPlaces = 0,
                Increment = 10m,
                Value = 200m
            };
            
            yPos += 80;
            
            // Notes
            var notesLabel = new Label
            {
                Text = "Notes (optional)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, yPos)
            };
            
            notesTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 60),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true,
                PlaceholderText = "Add any notes about your run..."
            };
            
            yPos += 110;
            
            // Buttons
            saveButton = new Button
            {
                Text = "Save Run",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(190, 45),
                Location = new Point(20, yPos),
                BackColor = Color.FromArgb(50, 127, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            saveButton.FlatAppearance.BorderSize = 0;
            saveButton.Click += SaveButton_Click;
            
            cancelButton = new Button
            {
                Text = "Cancel",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(190, 45),
                Location = new Point(230, yPos),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(16, 20, 25),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.Click += CancelButton_Click;
            
            // Add hover effects
            saveButton.MouseEnter += (s, e) => saveButton.BackColor = Color.FromArgb(37, 99, 235);
            saveButton.MouseLeave += (s, e) => saveButton.BackColor = Color.FromArgb(50, 127, 204);
            
            cancelButton.MouseEnter += (s, e) => cancelButton.BackColor = Color.FromArgb(209, 213, 219);
            cancelButton.MouseLeave += (s, e) => cancelButton.BackColor = Color.FromArgb(233, 237, 241);
            
            mainPanel.Controls.AddRange(new Control[] 
            { 
                titleLabel, resortLabel, resortComboBox, dateLabel, datePicker,
                distanceLabel, distanceNumeric, durationLabel, durationPanel,
                avgSpeedLabel, avgSpeedNumeric, maxSpeedLabel, maxSpeedNumeric,
                elevationLabel, elevationNumeric, notesLabel, notesTextBox,
                saveButton, cancelButton
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoadResorts()
        {
            if (resortComboBox == null) return;
            
            var resorts = resortRepository.GetAllResorts();
            
            resortComboBox.DisplayMember = "Name";
            resortComboBox.ValueMember = "Id";
            resortComboBox.DataSource = resorts;
            
            if (preselectedResortId.HasValue)
            {
                resortComboBox.SelectedValue = preselectedResortId.Value;
            }
        }
        
        private void SaveButton_Click(object? sender, EventArgs e)
        {
            if (UserSession.CurrentUser == null || resortComboBox?.SelectedValue == null)
            {
                MessageBox.Show("Please select a resort.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            var run = new SkiRun
            {
                UserId = UserSession.CurrentUser.Id,
                ResortId = (int)resortComboBox.SelectedValue,
                Date = datePicker?.Value ?? DateTime.Today,
                Distance = (double)(distanceNumeric?.Value ?? 1.0m),
                Duration = TimeSpan.FromHours((double)(hoursNumeric?.Value ?? 1)) + 
                          TimeSpan.FromMinutes((double)(minutesNumeric?.Value ?? 30)),
                AverageSpeed = (double)(avgSpeedNumeric?.Value ?? 25.0m),
                MaxSpeed = (double)(maxSpeedNumeric?.Value ?? 45.0m),
                ElevationChange = (double)(elevationNumeric?.Value ?? 200m),
                Notes = notesTextBox?.Text ?? "",
                ImagePath = ""
            };
            
            if (skiRunRepository.AddRun(run))
            {
                MessageBox.Show("Run recorded successfully!", "Success", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("Failed to save run. Please try again.", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
