<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-gray-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-gray-50 p-4 pb-2 justify-between">
          <div class="text-[#101419] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Select Resort</h2>
        </div>
        <div class="px-4 py-3">
          <label class="flex flex-col min-w-40 h-12 w-full">
            <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
              <div
                class="text-[#57738e] flex border-none bg-[#e9edf1] items-center justify-center pl-4 rounded-l-xl border-r-0"
                data-icon="MagnifyingGlass"
                data-size="24px"
                data-weight="regular"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                  <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                </svg>
              </div>
              <input
                placeholder="Search for a resort"
                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#101419] focus:outline-0 focus:ring-0 border-none bg-[#e9edf1] focus:border-none h-full placeholder:text-[#57738e] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                value=""
              />
            </div>
          </label>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Popular Resorts</h3>
        <div class="p-4">
          <div class="flex items-stretch justify-between gap-4 rounded-xl">
            <div class="flex flex-col gap-1 flex-[2_2_0px]">
              <p class="text-[#57738e] text-sm font-normal leading-normal">Colorado</p>
              <p class="text-[#101419] text-base font-bold leading-tight">Aspen Snowmass</p>
              <p class="text-[#57738e] text-sm font-normal leading-normal">5500 acres, 337 trails</p>
            </div>
            <div
              class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAAi32B2C1xk9I2mCBqbsOaxkIvrRPLlEZfrA4kAjDmETcV-hOEOhtwqp5UL9Ven1100r0jaiClDr2MzxXAimIvBCyzTEn4utMswOmQOJzudUXSUz8FSTnGwmWmOptzuE1h7fySKBwOGTgeK5iRWO46D6jTdgNm6hp37BhBWLJjhL--DnXSXHmr1tWeCjyNUUjpccJhTHZAgMuPhR8ESKGxOMO_Wzoh-78PwfGzy1sWq6VuSUpL4k-1QqlDSIXr2xmj0Jr4GSzxDEA");'
            ></div>
          </div>
        </div>
        <div class="p-4">
          <div class="flex items-stretch justify-between gap-4 rounded-xl">
            <div class="flex flex-col gap-1 flex-[2_2_0px]">
              <p class="text-[#57738e] text-sm font-normal leading-normal">Utah</p>
              <p class="text-[#101419] text-base font-bold leading-tight">Park City</p>
              <p class="text-[#57738e] text-sm font-normal leading-normal">7300 acres, 347 trails</p>
            </div>
            <div
              class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDc2jRJUtqWa_LxNZkKKMfNrGKfhkrsERyqd-jK1h7MQDxkLs6LHnlz4xk2GgmQoi2ycJs1Q4xVYQCmtH_ey8PcNXN_OWZjm0lhvj9ZN8JqVjuF9bLz_adUN0N-WyCmpHdmYGuUmMBOjxkjFEQfyUYXeZcF5c9F8BMcFVjKI2oEAWoyadakBkRSYwmt13icciOpd921X-Aw4yY5y3N5eUmUXR1Ar9lJEfaHe-TmGaIfdb-faMR9INIY3PHVsb0LH7tt0d3XI7AOU70");'
            ></div>
          </div>
        </div>
        <div class="p-4">
          <div class="flex items-stretch justify-between gap-4 rounded-xl">
            <div class="flex flex-col gap-1 flex-[2_2_0px]">
              <p class="text-[#57738e] text-sm font-normal leading-normal">California</p>
              <p class="text-[#101419] text-base font-bold leading-tight">Mammoth Mountain</p>
              <p class="text-[#57738e] text-sm font-normal leading-normal">3500 acres, 175 trails</p>
            </div>
            <div
              class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCvJ3WQlk6q2mbw2cwoJBbh-vd9-LkmwVFk8SQzFKTpXhLt4JC9uOvZI2BjnlCPXzJ47XAXG4bsJsOjHlhc2RfIx-N1OfA1bIDgSlgidnHxiBTWxZf9zOWUZULN9e_R3WzigqgfDjGdf5fcIaOvd_2JnfIdSrbN1VELs9MtE1UdhnPq1O8O9DW-Q-DwqiXVzBAT5eJJOSqChz_5a5BAwSGuJaABgP5LQkROto8404hnzABZzAIKpAaJQSMPfrWxdoVyNIXJlpfbuJY");'
            ></div>
          </div>
        </div>
        <div class="p-4">
          <div class="flex items-stretch justify-between gap-4 rounded-xl">
            <div class="flex flex-col gap-1 flex-[2_2_0px]">
              <p class="text-[#57738e] text-sm font-normal leading-normal">Vermont</p>
              <p class="text-[#101419] text-base font-bold leading-tight">Stowe Mountain Resort</p>
              <p class="text-[#57738e] text-sm font-normal leading-normal">485 acres, 116 trails</p>
            </div>
            <div
              class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAjf8C20ELHoiJ3z8mCBgfnyJ51BpkSoeiu-qF-Y_OqOtwDgjeI3R1GevvuFE8kTtq0102qfbzFH18AEY4yKSsUg_mfneyKrf4ZLk7ImaxsjfKRZcw-5lzHUelZbaTQR4mmfDFNaXUkzAOV6-Hn7ydo-xygCzrUGOGDHOOHBoCDwwt5onTasKzUHfCQ36Qs3dIWKeRxgaWAJCBC5i-mOqJUGLREpbRHRHm0Zgz-6wlwjh-aFWwvEWesgll0kvHwH6QCTtlPnvWhSV8");'
            ></div>
          </div>
        </div>
      </div>
      <div>
        <div class="flex gap-2 border-t border-[#e9edf1] bg-gray-50 px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#101419]" href="#">
            <div class="text-[#101419] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z"
                ></path>
              </svg>
            </div>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
              </svg>
            </div>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="Plus" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z"></path>
              </svg>
            </div>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
                ></path>
              </svg>
            </div>
          </a>
        </div>
        <div class="h-5 bg-gray-50"></div>
      </div>
    </div>
  </body>
</html>
