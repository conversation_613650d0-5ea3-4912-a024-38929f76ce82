<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-gray-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-gray-50 p-4 pb-2 justify-between">
          <div class="text-[#101419] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Run Tracker</h2>
        </div>
        <div class="flex px-4 py-3">
          <div
            class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl object-cover"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBGc86WBpE7Wyq2ZZGJiJ7RA1bU1cpnd-UQMU7wHaA5sHYy0xtX9t-mF4CtqywU5y8MB7EdZjRHpBoDAB758p22hiu3VpF3BufR0D0WBt7OcuzY52EPBcyIM3EcCwdZSP99QfNEM9MEzBLcNCiKR_dphQuhjfv_fpbsRkL5igYeNZTFRf3MXvTw0aFUc76K3iK3p1c5rB5wJqxTZJpIte-yU_hB4n5uHJ2XRr7UahbKZ-psbl7VJykKUK7LlJR-c06fO1Cv2agtxJI");'
          ></div>
        </div>
        <div class="flex flex-wrap gap-4 p-4">
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#e9edf1]">
            <p class="text-[#101419] text-base font-medium leading-normal">Total Distance</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">125 km</p>
          </div>
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#e9edf1]">
            <p class="text-[#101419] text-base font-medium leading-normal">Average Speed</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">35 km/h</p>
          </div>
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 bg-[#e9edf1]">
            <p class="text-[#101419] text-base font-medium leading-normal">Time on Slopes</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">15 hours</p>
          </div>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Recent Runs</h3>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="MapPin" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M128,64a40,40,0,1,0,40,40A40,40,0,0,0,128,64Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,128Zm0-112a88.1,88.1,0,0,0-88,88c0,31.4,14.51,64.68,42,96.25a254.19,254.19,0,0,0,41.45,38.3,8,8,0,0,0,9.18,0A254.19,254.19,0,0,0,174,200.25c27.45-31.57,42-64.85,42-96.25A88.1,88.1,0,0,0,128,16Zm0,206c-16.53-13-72-60.75-72-118a72,72,0,0,1,144,0C200,161.23,144.53,209,128,222Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">January 20, 2024</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Snowy Peaks Resort</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="MapPin" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M128,64a40,40,0,1,0,40,40A40,40,0,0,0,128,64Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,128Zm0-112a88.1,88.1,0,0,0-88,88c0,31.4,14.51,64.68,42,96.25a254.19,254.19,0,0,0,41.45,38.3,8,8,0,0,0,9.18,0A254.19,254.19,0,0,0,174,200.25c27.45-31.57,42-64.85,42-96.25A88.1,88.1,0,0,0,128,16Zm0,206c-16.53-13-72-60.75-72-118a72,72,0,0,1,144,0C200,161.23,144.53,209,128,222Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">January 15, 2024</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Alpine Valley</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="MapPin" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M128,64a40,40,0,1,0,40,40A40,40,0,0,0,128,64Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,128Zm0-112a88.1,88.1,0,0,0-88,88c0,31.4,14.51,64.68,42,96.25a254.19,254.19,0,0,0,41.45,38.3,8,8,0,0,0,9.18,0A254.19,254.19,0,0,0,174,200.25c27.45-31.57,42-64.85,42-96.25A88.1,88.1,0,0,0,128,16Zm0,206c-16.53-13-72-60.75-72-118a72,72,0,0,1,144,0C200,161.23,144.53,209,128,222Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">January 10, 2024</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Mountain High</p>
          </div>
        </div>
      </div>
      <div>
        <div class="flex gap-2 border-t border-[#e9edf1] bg-gray-50 px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#101419]" href="#">
            <div class="text-[#101419] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z"
                ></path>
              </svg>
            </div>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="MapTrifold" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M228.92,49.69a8,8,0,0,0-6.86-1.45L160.93,63.52,99.58,32.84a8,8,0,0,0-5.52-.6l-64,16A8,8,0,0,0,24,56V200a8,8,0,0,0,9.94,7.76l61.13-15.28,61.35,30.68A8.15,8.15,0,0,0,160,224a8,8,0,0,0,1.94-.24l64-16A8,8,0,0,0,232,200V56A8,8,0,0,0,228.92,49.69ZM104,52.94l48,24V203.06l-48-24ZM40,62.25l48-12v127.5l-48,12Zm176,131.5-48,12V78.25l48-12Z"
                ></path>
              </svg>
            </div>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="Plus" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z"></path>
              </svg>
            </div>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
                ></path>
              </svg>
            </div>
          </a>
        </div>
        <div class="h-5 bg-gray-50"></div>
      </div>
    </div>
  </body>
</html>
