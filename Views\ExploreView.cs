using SkiTrackApp.Data;
using SkiTrackApp.Models;

namespace SkiTrackApp.Forms
{
    public partial class ExploreView : UserControl
    {
        private readonly ResortRepository resortRepository;
        private TextBox? searchTextBox;
        private Panel? resortsPanel;
        private List<Resort> allResorts = new();
        
        public ExploreView()
        {
            InitializeComponent();
            resortRepository = new ResortRepository();
            SetupView();
            LoadResorts();
        }
        
        private void SetupView()
        {
            this.BackColor = Color.FromArgb(249, 250, 251);
            this.Dock = DockStyle.Fill;
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            
            // Header
            var headerLabel = new Label
            {
                Text = "Explore Resorts",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Search box
            var searchLabel = new Label
            {
                Text = "Search resorts...",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 20),
                Location = new Point(20, 70)
            };
            
            searchTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 35),
                Location = new Point(20, 95),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "Search by name, location, or state..."
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;
            
            // Popular resorts title
            var popularTitle = new Label
            {
                Text = "Popular Resorts",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 150),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            // Resorts panel
            resortsPanel = new Panel
            {
                Size = new Size(400, 400),
                Location = new Point(20, 190),
                BackColor = Color.Transparent,
                AutoScroll = true
            };
            
            mainPanel.Controls.AddRange(new Control[] 
            { 
                headerLabel, searchLabel, searchTextBox, popularTitle, resortsPanel 
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoadResorts()
        {
            allResorts = resortRepository.GetAllResorts();
            DisplayResorts(allResorts);
        }
        
        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(searchTextBox?.Text))
            {
                DisplayResorts(allResorts);
            }
            else
            {
                var filteredResorts = resortRepository.SearchResorts(searchTextBox.Text);
                DisplayResorts(filteredResorts);
            }
        }
        
        private void DisplayResorts(List<Resort> resorts)
        {
            if (resortsPanel == null) return;
            
            resortsPanel.Controls.Clear();
            
            if (!resorts.Any())
            {
                var noResortsLabel = new Label
                {
                    Text = "No resorts found.",
                    Font = new Font("Segoe UI", 11),
                    ForeColor = Color.FromArgb(107, 114, 128),
                    Size = new Size(380, 60),
                    Location = new Point(10, 50),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                resortsPanel.Controls.Add(noResortsLabel);
                return;
            }
            
            int yPos = 0;
            foreach (var resort in resorts)
            {
                var resortPanel = CreateResortCard(resort, yPos);
                resortsPanel.Controls.Add(resortPanel);
                yPos += 120;
            }
        }
        
        private Panel CreateResortCard(Resort resort, int yPosition)
        {
            var panel = new Panel
            {
                Size = new Size(380, 110),
                Location = new Point(0, yPosition),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Cursor = Cursors.Hand
            };
            
            // Image placeholder
            var imagePanel = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(10, 15),
                BackColor = Color.FromArgb(229, 231, 235),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var imageLabel = new Label
            {
                Text = "🏔️",
                Font = new Font("Segoe UI", 20),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            imagePanel.Controls.Add(imageLabel);
            
            // Resort name
            var nameLabel = new Label
            {
                Text = resort.Name,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(270, 25),
                Location = new Point(100, 15)
            };
            
            // Location
            var locationLabel = new Label
            {
                Text = resort.FullLocation,
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(270, 20),
                Location = new Point(100, 40)
            };
            
            // Trail info
            var trailInfoLabel = new Label
            {
                Text = resort.TrailInfo,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(270, 20),
                Location = new Point(100, 60)
            };
            
            // Popular badge
            if (resort.IsPopular)
            {
                var popularBadge = new Label
                {
                    Text = "⭐ Popular",
                    Font = new Font("Segoe UI", 8, FontStyle.Bold),
                    ForeColor = Color.FromArgb(245, 158, 11),
                    BackColor = Color.FromArgb(254, 243, 199),
                    Size = new Size(60, 20),
                    Location = new Point(100, 80),
                    TextAlign = ContentAlignment.MiddleCenter,
                    BorderStyle = BorderStyle.FixedSingle
                };
                panel.Controls.Add(popularBadge);
            }
            
            panel.Controls.AddRange(new Control[] { imagePanel, nameLabel, locationLabel, trailInfoLabel });
            
            // Click event to select resort
            panel.Click += (s, e) => SelectResort(resort);
            
            return panel;
        }
        
        private void SelectResort(Resort resort)
        {
            var result = MessageBox.Show(
                $"Would you like to record a run at {resort.Name}?",
                "Select Resort",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );
            
            if (result == DialogResult.Yes)
            {
                var recordForm = new RecordRunForm(resort.Id);
                recordForm.ShowDialog();
            }
        }
    }
}
