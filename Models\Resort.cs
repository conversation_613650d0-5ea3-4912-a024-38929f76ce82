namespace SkiTrackApp.Models
{
    public class Resort
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public int TotalAcres { get; set; }
        public int TotalTrails { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsPopular { get; set; }
        
        public string FullLocation => $"{Location}, {State}";
        public string TrailInfo => $"{TotalAcres} acres, {TotalTrails} trails";
    }
}
