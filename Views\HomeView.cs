using SkiTrackApp.Data;
using SkiTrackApp.Services;
using SkiTrackApp.Models;

namespace SkiTrackApp.Forms
{
    public partial class HomeView : UserControl
    {
        private readonly SkiRunRepository skiRunRepository;
        private Panel? statsPanel;
        private Panel? recentRunsPanel;
        
        public HomeView()
        {
            InitializeComponent();
            skiRunRepository = new SkiRunRepository();
            SetupView();
            LoadData();
        }
        
        private void SetupView()
        {
            this.BackColor = Color.FromArgb(249, 250, 251);
            this.Dock = DockStyle.Fill;
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            
            // Header
            var headerLabel = new Label
            {
                Text = "Run Tracker",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Hero image placeholder
            var heroPanel = new Panel
            {
                Size = new Size(400, 150),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(229, 231, 235),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var heroLabel = new Label
            {
                Text = "🎿 Your Ski Adventures",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(107, 114, 128),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            heroPanel.Controls.Add(heroLabel);
            
            // Statistics panel
            statsPanel = new Panel
            {
                Size = new Size(400, 120),
                Location = new Point(20, 230),
                BackColor = Color.Transparent
            };
            
            // Recent runs title
            var recentRunsTitle = new Label
            {
                Text = "Recent Runs",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 370),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            // Recent runs panel
            recentRunsPanel = new Panel
            {
                Size = new Size(400, 200),
                Location = new Point(20, 410),
                BackColor = Color.Transparent,
                AutoScroll = true
            };
            
            mainPanel.Controls.AddRange(new Control[] 
            { 
                headerLabel, heroPanel, statsPanel, recentRunsTitle, recentRunsPanel 
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoadData()
        {
            if (UserSession.CurrentUser == null) return;
            
            LoadStatistics();
            LoadRecentRuns();
        }
        
        private void LoadStatistics()
        {
            if (UserSession.CurrentUser == null || statsPanel == null) return;
            
            var stats = skiRunRepository.GetUserStatistics(UserSession.CurrentUser.Id);
            
            statsPanel.Controls.Clear();
            
            // Total Distance
            var distancePanel = CreateStatCard("Total Distance", stats.FormattedTotalDistance, 0);
            
            // Average Speed
            var speedPanel = CreateStatCard("Average Speed", stats.FormattedAverageSpeed, 140);
            
            // Time on Slopes
            var timePanel = CreateStatCard("Time on Slopes", stats.FormattedTotalTime, 280);
            
            statsPanel.Controls.AddRange(new Control[] { distancePanel, speedPanel, timePanel });
        }
        
        private Panel CreateStatCard(string title, string value, int xPosition)
        {
            var panel = new Panel
            {
                Size = new Size(120, 100),
                Location = new Point(xPosition, 0),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(110, 30),
                Location = new Point(5, 10),
                TextAlign = ContentAlignment.TopCenter
            };
            
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(110, 40),
                Location = new Point(5, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            panel.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            
            return panel;
        }
        
        private void LoadRecentRuns()
        {
            if (UserSession.CurrentUser == null || recentRunsPanel == null) return;
            
            var runs = skiRunRepository.GetUserRuns(UserSession.CurrentUser.Id).Take(3).ToList();
            
            recentRunsPanel.Controls.Clear();
            
            if (!runs.Any())
            {
                var noRunsLabel = new Label
                {
                    Text = "No runs recorded yet. Start tracking your ski adventures!",
                    Font = new Font("Segoe UI", 11),
                    ForeColor = Color.FromArgb(107, 114, 128),
                    Size = new Size(380, 60),
                    Location = new Point(10, 50),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                recentRunsPanel.Controls.Add(noRunsLabel);
                return;
            }
            
            int yPos = 0;
            foreach (var run in runs)
            {
                var runPanel = CreateRunCard(run, yPos);
                recentRunsPanel.Controls.Add(runPanel);
                yPos += 70;
            }
        }
        
        private Panel CreateRunCard(SkiRun run, int yPosition)
        {
            var panel = new Panel
            {
                Size = new Size(380, 60),
                Location = new Point(0, yPosition),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Cursor = Cursors.Hand
            };
            
            // Icon
            var iconLabel = new Label
            {
                Text = "📍",
                Font = new Font("Segoe UI", 16),
                Size = new Size(40, 40),
                Location = new Point(10, 10),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(233, 237, 241)
            };
            
            // Date
            var dateLabel = new Label
            {
                Text = run.FormattedDate,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(200, 20),
                Location = new Point(60, 10)
            };
            
            // Resort
            var resortLabel = new Label
            {
                Text = run.Resort?.Name ?? "Unknown Resort",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(200, 20),
                Location = new Point(60, 30)
            };
            
            // Distance
            var distanceLabel = new Label
            {
                Text = $"{run.Distance:F1} km",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(80, 20),
                Location = new Point(280, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            panel.Controls.AddRange(new Control[] { iconLabel, dateLabel, resortLabel, distanceLabel });
            
            // Click event to show run details
            panel.Click += (s, e) => ShowRunDetails(run.Id);
            
            return panel;
        }
        
        private void ShowRunDetails(int runId)
        {
            var runDetailsForm = new RunDetailsForm(runId);
            runDetailsForm.ShowDialog();
        }
    }
}
