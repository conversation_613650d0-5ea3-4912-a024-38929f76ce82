using SkiTrackApp.Data;
using SkiTrackApp.Services;

namespace SkiTrackApp.Forms
{
    public partial class LoginForm : Form
    {
        private TextBox? emailTextBox;
        private TextBox? passwordTextBox;
        private Button? loginButton;
        private Button? signUpButton;
        private Label? forgotPasswordLabel;
        private readonly UserRepository userRepository;
        
        public LoginForm()
        {
            InitializeComponent();
            userRepository = new UserRepository();
            SetupForm();
        }
        
        private void SetupForm()
        {
            this.Text = "SkiTrack - Welcome";
            this.Size = new Size(400, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(249, 250, 251);
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            // Main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };
            
            // Hero image placeholder
            var heroPanel = new Panel
            {
                Size = new Size(360, 150),
                Location = new Point(20, 30),
                BackColor = Color.FromArgb(229, 231, 235),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var heroLabel = new Label
            {
                Text = "🎿 SkiTrack",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(107, 114, 128),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            heroPanel.Controls.Add(heroLabel);
            
            // Welcome title
            var titleLabel = new Label
            {
                Text = "Welcome to SkiTrack",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(360, 30),
                Location = new Point(20, 200),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Email input
            var emailLabel = new Label
            {
                Text = "Email or Username",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, 250)
            };
            
            emailTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(360, 35),
                Location = new Point(20, 275),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10)
            };
            
            // Password input
            var passwordLabel = new Label
            {
                Text = "Password",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, 320)
            };
            
            passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(360, 35),
                Location = new Point(20, 345),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true,
                Padding = new Padding(10)
            };
            
            // Forgot password link
            forgotPasswordLabel = new Label
            {
                Text = "Forgot Password?",
                Font = new Font("Segoe UI", 9, FontStyle.Underline),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, 390),
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand
            };
            
            // Login button
            loginButton = new Button
            {
                Text = "Log In",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(360, 45),
                Location = new Point(20, 420),
                BackColor = Color.FromArgb(50, 127, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;
            
            // Sign up button
            signUpButton = new Button
            {
                Text = "Sign Up",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(360, 45),
                Location = new Point(20, 475),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(16, 20, 25),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            signUpButton.FlatAppearance.BorderSize = 0;
            signUpButton.Click += SignUpButton_Click;
            
            // Add hover effects
            loginButton.MouseEnter += (s, e) => loginButton.BackColor = Color.FromArgb(37, 99, 235);
            loginButton.MouseLeave += (s, e) => loginButton.BackColor = Color.FromArgb(50, 127, 204);
            
            signUpButton.MouseEnter += (s, e) => signUpButton.BackColor = Color.FromArgb(209, 213, 219);
            signUpButton.MouseLeave += (s, e) => signUpButton.BackColor = Color.FromArgb(233, 237, 241);
            
            // Add controls to main panel
            mainPanel.Controls.AddRange(new Control[] 
            { 
                heroPanel, titleLabel, emailLabel, emailTextBox, 
                passwordLabel, passwordTextBox, forgotPasswordLabel, 
                loginButton, signUpButton 
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoginButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(emailTextBox?.Text) || string.IsNullOrWhiteSpace(passwordTextBox?.Text))
            {
                MessageBox.Show("Please enter both email/username and password.", "Login Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            var user = userRepository.AuthenticateUser(emailTextBox.Text, passwordTextBox.Text);
            if (user != null)
            {
                UserSession.CurrentUser = user;
                var mainForm = new MainForm();
                mainForm.Show();
                this.Hide();
            }
            else
            {
                MessageBox.Show("Invalid email/username or password.", "Login Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void SignUpButton_Click(object? sender, EventArgs e)
        {
            var signUpForm = new SignUpForm();
            signUpForm.Show();
            this.Hide();
        }
    }
}
