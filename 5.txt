<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-gray-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-gray-50 p-4 pb-2 justify-between">
          <div class="text-[#101419] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Run Details</h2>
        </div>
        <div class="flex px-4 py-3">
          <div
            class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl object-cover"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB5KpoiEq0ik1LW2P6y3XLWRzPf3GhgQ9D3qFa5VTBbYdDECIiw3W2yRRzBPJqQli088xhq0y1MtFzHwHpkABIa8CedgidPXJRkepy9K6JWCBJQOTjJW-5tXdyPlIVTvkN_gW5LlJL4FAU9fSICIrWPi9JJ9Y6k7iEMOT0VN5OpbaZJ_z4V01adB7MWom4a_uPkD96y928UUKKkamyLSWbjP64HtFcyq-HBUvw0JdqxY0NwpbGGROx_PD7ovMc9YaUqqDVIPVa22iI");'
          ></div>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Stats</h3>
        <div class="flex flex-wrap gap-4 p-4">
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#d3dbe4]">
            <p class="text-[#101419] text-base font-medium leading-normal">Distance</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">5.2 km</p>
          </div>
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#d3dbe4]">
            <p class="text-[#101419] text-base font-medium leading-normal">Total Time</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">2h 15m</p>
          </div>
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#d3dbe4]">
            <p class="text-[#101419] text-base font-medium leading-normal">Avg Speed</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">23 km/h</p>
          </div>
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#d3dbe4]">
            <p class="text-[#101419] text-base font-medium leading-normal">Max Speed</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">65 km/h</p>
          </div>
          <div class="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#d3dbe4]">
            <p class="text-[#101419] text-base font-medium leading-normal">Elevation Change</p>
            <p class="text-[#101419] tracking-light text-2xl font-bold leading-tight">1200 m</p>
          </div>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Speed &amp; Elevation</h3>
        <div class="flex flex-wrap gap-4 px-4 py-6">
          <div class="flex min-w-72 flex-1 flex-col gap-2">
            <p class="text-[#101419] text-base font-medium leading-normal">Speed</p>
            <p class="text-[#101419] tracking-light text-[32px] font-bold leading-tight truncate">23 km/h</p>
            <div class="flex gap-1">
              <p class="text-[#57738e] text-base font-normal leading-normal">2h 15m</p>
              <p class="text-[#078838] text-base font-medium leading-normal">+10%</p>
            </div>
            <div class="flex min-h-[180px] flex-1 flex-col gap-8 py-4">
              <svg width="100%" height="148" viewBox="-3 0 478 150" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                <path
                  d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25V149H326.769H0V109Z"
                  fill="url(#paint0_linear_1131_5935)"
                ></path>
                <path
                  d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25"
                  stroke="#57738e"
                  stroke-width="3"
                  stroke-linecap="round"
                ></path>
                <defs>
                  <linearGradient id="paint0_linear_1131_5935" x1="236" y1="1" x2="236" y2="149" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#e9edf1"></stop>
                    <stop offset="1" stop-color="#e9edf1" stop-opacity="0"></stop>
                  </linearGradient>
                </defs>
              </svg>
              <div class="flex justify-around">
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">0m</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">30m</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">1h</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">1h 30m</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">2h</p>
              </div>
            </div>
          </div>
          <div class="flex min-w-72 flex-1 flex-col gap-2">
            <p class="text-[#101419] text-base font-medium leading-normal">Elevation</p>
            <p class="text-[#101419] tracking-light text-[32px] font-bold leading-tight truncate">1200 m</p>
            <div class="flex gap-1">
              <p class="text-[#57738e] text-base font-normal leading-normal">2h 15m</p>
              <p class="text-[#e73908] text-base font-medium leading-normal">-5%</p>
            </div>
            <div class="flex min-h-[180px] flex-1 flex-col gap-8 py-4">
              <svg width="100%" height="148" viewBox="-3 0 478 150" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
                <path
                  d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25V149H326.769H0V109Z"
                  fill="url(#paint0_linear_1131_5935)"
                ></path>
                <path
                  d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25"
                  stroke="#57738e"
                  stroke-width="3"
                  stroke-linecap="round"
                ></path>
                <defs>
                  <linearGradient id="paint0_linear_1131_5935" x1="236" y1="1" x2="236" y2="149" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#e9edf1"></stop>
                    <stop offset="1" stop-color="#e9edf1" stop-opacity="0"></stop>
                  </linearGradient>
                </defs>
              </svg>
              <div class="flex justify-around">
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">0m</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">30m</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">1h</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">1h 30m</p>
                <p class="text-[#57738e] text-[13px] font-bold leading-normal tracking-[0.015em]">2h</p>
              </div>
            </div>
          </div>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Actions</h3>
        <div class="@container">
          <div class="gap-2 px-4 grid-cols-[repeat(auto-fit, minmax(80px,_1fr))] grid">
            <div class="flex flex-col items-center gap-2 bg-gray-50 py-2.5 text-center">
              <div class="rounded-full bg-[#e9edf1] p-2.5">
                <div class="text-[#101419]" data-icon="Note" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M88,96a8,8,0,0,1,8-8h64a8,8,0,0,1,0,16H96A8,8,0,0,1,88,96Zm8,40h64a8,8,0,0,0,0-16H96a8,8,0,0,0,0,16Zm32,16H96a8,8,0,0,0,0,16h32a8,8,0,0,0,0-16ZM224,48V156.69A15.86,15.86,0,0,1,219.31,168L168,219.31A15.86,15.86,0,0,1,156.69,224H48a16,16,0,0,1-16-16V48A16,16,0,0,1,48,32H208A16,16,0,0,1,224,48ZM48,208H152V160a8,8,0,0,1,8-8h48V48H48Zm120-40v28.7L196.69,168Z"
                    ></path>
                  </svg>
                </div>
              </div>
              <p class="text-[#101419] text-sm font-medium leading-normal">Add Note</p>
            </div>
            <div class="flex flex-col items-center gap-2 bg-gray-50 py-2.5 text-center">
              <div class="rounded-full bg-[#e9edf1] p-2.5">
                <div class="text-[#101419]" data-icon="Image" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,16V158.75l-26.07-26.06a16,16,0,0,0-22.63,0l-20,20-44-44a16,16,0,0,0-22.62,0L40,149.37V56ZM40,172l52-52,80,80H40Zm176,28H194.63l-36-36,20-20L216,181.38V200ZM144,100a12,12,0,1,1,12,12A12,12,0,0,1,144,100Z"
                    ></path>
                  </svg>
                </div>
              </div>
              <p class="text-[#101419] text-sm font-medium leading-normal">Add Photo</p>
            </div>
            <div class="flex flex-col items-center gap-2 bg-gray-50 py-2.5 text-center">
              <div class="rounded-full bg-[#e9edf1] p-2.5">
                <div class="text-[#101419]" data-icon="Share" data-size="20px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M229.66,109.66l-48,48a8,8,0,0,1-11.32-11.32L204.69,112H165a88,88,0,0,0-85.23,66,8,8,0,0,1-15.5-4A103.94,103.94,0,0,1,165,96h39.71L170.34,61.66a8,8,0,0,1,11.32-11.32l48,48A8,8,0,0,1,229.66,109.66ZM192,208H40V88a8,8,0,0,0-16,0V208a16,16,0,0,0,16,16H192a8,8,0,0,0,0-16Z"
                    ></path>
                  </svg>
                </div>
              </div>
              <p class="text-[#101419] text-sm font-medium leading-normal">Share</p>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="flex gap-2 border-t border-[#e9edf1] bg-gray-50 px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#57738e] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
              </svg>
            </div>
            <p class="text-[#57738e] text-xs font-medium leading-normal tracking-[0.015em]">Explore</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#101419]" href="#">
            <div class="text-[#101419] flex h-8 items-center justify-center" data-icon="RewindCircle" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm48,132a8,8,0,0,1-12.59,6.55l-40-28A8,8,0,0,1,120,128v28a8,8,0,0,1-12.59,6.55l-40-28a8,8,0,0,1,0-13.1l40-28A8,8,0,0,1,120,100v28a8,8,0,0,1,3.41-6.55l40-28A8,8,0,0,1,176,100Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#101419] text-xs font-medium leading-normal tracking-[0.015em]">Record</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#57738e] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </a>
        </div>
        <div class="h-5 bg-gray-50"></div>
      </div>
    </div>
  </body>
</html>
