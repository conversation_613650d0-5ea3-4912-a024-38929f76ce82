using SkiTrackApp.Services;
using SkiTrackApp.Data;

namespace SkiTrackApp.Forms
{
    public partial class ProfileView : UserControl
    {
        private readonly SkiRunRepository skiRunRepository;
        
        public ProfileView()
        {
            InitializeComponent();
            skiRunRepository = new SkiRunRepository();
            SetupView();
            LoadProfile();
        }
        
        private void SetupView()
        {
            this.BackColor = Color.FromArgb(249, 250, 251);
            this.Dock = DockStyle.Fill;
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            
            // Header
            var headerLabel = new Label
            {
                Text = "Profile",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Profile picture placeholder
            var profilePicPanel = new Panel
            {
                Size = new Size(100, 100),
                Location = new Point(170, 70),
                BackColor = Color.FromArgb(229, 231, 235),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var profilePicLabel = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI", 40),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                ForeColor = Color.FromArgb(107, 114, 128)
            };
            profilePicPanel.Controls.Add(profilePicLabel);
            
            // User name
            var nameLabel = new Label
            {
                Text = UserSession.CurrentUser?.FullName ?? "User",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 190),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Username
            var usernameLabel = new Label
            {
                Text = $"@{UserSession.CurrentUser?.Username ?? "username"}",
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(400, 25),
                Location = new Point(20, 220),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Statistics section
            var statsTitle = new Label
            {
                Text = "Statistics",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 270),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            // Stats panel
            var statsPanel = new Panel
            {
                Size = new Size(400, 120),
                Location = new Point(20, 310),
                BackColor = Color.Transparent
            };
            
            LoadStatistics(statsPanel);
            
            // Settings section
            var settingsTitle = new Label
            {
                Text = "Settings",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 450),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            // Edit Profile button
            var editProfileButton = new Button
            {
                Text = "Edit Profile",
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 40),
                Location = new Point(20, 490),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(16, 20, 25),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            editProfileButton.FlatAppearance.BorderSize = 0;
            editProfileButton.Click += EditProfileButton_Click;
            
            // Logout button
            var logoutButton = new Button
            {
                Text = "Logout",
                Font = new Font("Segoe UI", 11),
                Size = new Size(400, 40),
                Location = new Point(20, 540),
                BackColor = Color.FromArgb(239, 68, 68),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            logoutButton.FlatAppearance.BorderSize = 0;
            logoutButton.Click += LogoutButton_Click;
            
            // Add hover effects
            editProfileButton.MouseEnter += (s, e) => editProfileButton.BackColor = Color.FromArgb(209, 213, 219);
            editProfileButton.MouseLeave += (s, e) => editProfileButton.BackColor = Color.FromArgb(233, 237, 241);
            
            logoutButton.MouseEnter += (s, e) => logoutButton.BackColor = Color.FromArgb(220, 38, 38);
            logoutButton.MouseLeave += (s, e) => logoutButton.BackColor = Color.FromArgb(239, 68, 68);
            
            mainPanel.Controls.AddRange(new Control[] 
            { 
                headerLabel, profilePicPanel, nameLabel, usernameLabel, 
                statsTitle, statsPanel, settingsTitle, editProfileButton, logoutButton 
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoadProfile()
        {
            // Profile data is loaded in CreateControls method
        }
        
        private void LoadStatistics(Panel statsPanel)
        {
            if (UserSession.CurrentUser == null) return;
            
            var stats = skiRunRepository.GetUserStatistics(UserSession.CurrentUser.Id);
            
            // Total Runs
            var runsPanel = CreateStatCard("Total Runs", stats.TotalRuns.ToString(), 0);
            
            // Total Distance
            var distancePanel = CreateStatCard("Total Distance", stats.FormattedTotalDistance, 133);
            
            // Total Time
            var timePanel = CreateStatCard("Total Time", stats.FormattedTotalTime, 266);
            
            statsPanel.Controls.AddRange(new Control[] { runsPanel, distancePanel, timePanel });
        }
        
        private Panel CreateStatCard(string title, string value, int xPosition)
        {
            var panel = new Panel
            {
                Size = new Size(120, 100),
                Location = new Point(xPosition, 0),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(110, 30),
                Location = new Point(5, 15),
                TextAlign = ContentAlignment.TopCenter
            };
            
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(110, 40),
                Location = new Point(5, 45),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            panel.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            
            return panel;
        }
        
        private void EditProfileButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("Edit Profile feature coming soon!", "Info", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private void LogoutButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to logout?", "Logout", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                UserSession.Logout();
                
                var loginForm = new LoginForm();
                loginForm.Show();
                
                // Close the main form
                var mainForm = this.FindForm();
                mainForm?.Hide();
            }
        }
    }
}
