namespace SkiTrackApp.Models
{
    public class SkiRun
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int ResortId { get; set; }
        public DateTime Date { get; set; }
        public double Distance { get; set; } // in kilometers
        public TimeSpan Duration { get; set; }
        public double AverageSpeed { get; set; } // km/h
        public double MaxSpeed { get; set; } // km/h
        public double ElevationChange { get; set; } // in meters
        public string Notes { get; set; } = string.Empty;
        public string ImagePath { get; set; } = string.Empty;
        
        // Navigation properties
        public User? User { get; set; }
        public Resort? Resort { get; set; }
        
        // Calculated properties
        public string FormattedDuration => $"{Duration.Hours}h {Duration.Minutes}m";
        public string FormattedDate => Date.ToString("MMMM dd, yyyy");
    }
}
