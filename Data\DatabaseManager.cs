using System.Data.SQLite;
using SkiTrackApp.Models;

namespace SkiTrackApp.Data
{
    public static class DatabaseManager
    {
        private static readonly string ConnectionString = "Data Source=skitrack.db;Version=3;";
        
        public static void InitializeDatabase()
        {
            using var connection = new SQLiteConnection(ConnectionString);
            connection.Open();
            
            CreateTables(connection);
            SeedData(connection);
        }
        
        private static void CreateTables(SQLiteConnection connection)
        {
            // Users table
            var createUsersTable = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT UNIQUE NOT NULL,
                    Email TEXT UNIQUE NOT NULL,
                    PasswordHash TEXT NOT NULL,
                    FirstName TEXT,
                    LastName TEXT,
                    ProfileImagePath TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastLoginAt DATETIME
                )";
            
            // Resorts table
            var createResortsTable = @"
                CREATE TABLE IF NOT EXISTS Resorts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Location TEXT NOT NULL,
                    State TEXT NOT NULL,
                    Country TEXT NOT NULL,
                    TotalAcres INTEGER,
                    TotalTrails INTEGER,
                    ImageUrl TEXT,
                    Description TEXT,
                    IsPopular BOOLEAN DEFAULT 0
                )";
            
            // SkiRuns table
            var createSkiRunsTable = @"
                CREATE TABLE IF NOT EXISTS SkiRuns (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    ResortId INTEGER NOT NULL,
                    Date DATETIME NOT NULL,
                    Distance REAL NOT NULL,
                    Duration TEXT NOT NULL,
                    AverageSpeed REAL NOT NULL,
                    MaxSpeed REAL NOT NULL,
                    ElevationChange REAL NOT NULL,
                    Notes TEXT,
                    ImagePath TEXT,
                    FOREIGN KEY (UserId) REFERENCES Users(Id),
                    FOREIGN KEY (ResortId) REFERENCES Resorts(Id)
                )";
            
            using var command = new SQLiteCommand(createUsersTable, connection);
            command.ExecuteNonQuery();
            
            command.CommandText = createResortsTable;
            command.ExecuteNonQuery();
            
            command.CommandText = createSkiRunsTable;
            command.ExecuteNonQuery();
        }
        
        private static void SeedData(SQLiteConnection connection)
        {
            // Check if data already exists
            var checkCommand = new SQLiteCommand("SELECT COUNT(*) FROM Resorts", connection);
            var count = Convert.ToInt32(checkCommand.ExecuteScalar());
            
            if (count > 0) return; // Data already seeded
            
            // Seed resorts data
            var resorts = new[]
            {
                new { Name = "Aspen Snowmass", Location = "Aspen", State = "Colorado", Country = "USA", Acres = 5500, Trails = 337, IsPopular = true },
                new { Name = "Park City", Location = "Park City", State = "Utah", Country = "USA", Acres = 7300, Trails = 347, IsPopular = true },
                new { Name = "Mammoth Mountain", Location = "Mammoth Lakes", State = "California", Country = "USA", Acres = 3500, Trails = 175, IsPopular = true },
                new { Name = "Stowe Mountain Resort", Location = "Stowe", State = "Vermont", Country = "USA", Acres = 485, Trails = 116, IsPopular = true }
            };
            
            foreach (var resort in resorts)
            {
                var insertCommand = new SQLiteCommand(@"
                    INSERT INTO Resorts (Name, Location, State, Country, TotalAcres, TotalTrails, IsPopular)
                    VALUES (@name, @location, @state, @country, @acres, @trails, @popular)", connection);
                
                insertCommand.Parameters.AddWithValue("@name", resort.Name);
                insertCommand.Parameters.AddWithValue("@location", resort.Location);
                insertCommand.Parameters.AddWithValue("@state", resort.State);
                insertCommand.Parameters.AddWithValue("@country", resort.Country);
                insertCommand.Parameters.AddWithValue("@acres", resort.Acres);
                insertCommand.Parameters.AddWithValue("@trails", resort.Trails);
                insertCommand.Parameters.AddWithValue("@popular", resort.IsPopular);
                
                insertCommand.ExecuteNonQuery();
            }
        }
        
        public static SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(ConnectionString);
        }
    }
}
