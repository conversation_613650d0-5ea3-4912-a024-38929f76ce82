using SkiTrackApp.Forms;
using SkiTrackApp.Data;

namespace SkiTrackApp
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // إضافة معالج إنهاء التطبيق لحفظ قاعدة البيانات
            Application.ApplicationExit += Application_ApplicationExit;
            AppDomain.CurrentDomain.ProcessExit += CurrentDomain_ProcessExit;

            // معالج إغلاق النافذة بـ Ctrl+C أو إغلاق Console
            Console.CancelKeyPress += Console_CancelKeyPress;

            try
            {
                // Initialize database
                DatabaseManager.InitializeDatabase();

                Application.Run(new WelcomeForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // تأكد من إغلاق قاعدة البيانات عند إنهاء التطبيق
                DatabaseManager.CloseDatabase();
            }
        }

        private static void Application_ApplicationExit(object? sender, EventArgs e)
        {
            // حفظ وإغلاق قاعدة البيانات عند إنهاء التطبيق
            DatabaseManager.CloseDatabase();
        }

        private static void CurrentDomain_ProcessExit(object? sender, EventArgs e)
        {
            // حفظ وإغلاق قاعدة البيانات عند إنهاء العملية
            DatabaseManager.CloseDatabase();
        }

        private static void Console_CancelKeyPress(object? sender, ConsoleCancelEventArgs e)
        {
            // حفظ وإغلاق قاعدة البيانات عند الضغط على Ctrl+C
            DatabaseManager.CloseDatabase();
        }
    }
}
