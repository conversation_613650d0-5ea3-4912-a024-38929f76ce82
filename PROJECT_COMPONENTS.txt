===============================================
           مكونات مشروع SkiTrack
===============================================

📁 هيكل المشروع:

SkiTrackApp/
├── 📄 Program.cs                    # نقطة البداية للتطبيق
├── 📄 SkiTrackApp.csproj           # ملف إعدادات المشروع
├── 📁 Models/                      # نماذج البيانات
│   ├── User.cs                     # نموذج المستخدم
│   ├── Resort.cs                   # نموذج المنتجع
│   ├── SkiRun.cs                   # نموذج رحلة التزلج
│   └── RunStatistics.cs            # نموذج الإحصائيات
├── 📁 Data/                        # طبقة الوصول للبيانات
│   ├── DatabaseManager.cs          # إدارة قاعدة البيانات
│   ├── UserRepository.cs           # عمليات المستخدمين
│   ├── ResortRepository.cs         # عمليات المنتجعات
│   └── SkiRunRepository.cs         # عمليات الرحلات
├── 📁 Services/                    # الخدمات
│   └── UserSession.cs              # إدارة جلسة المستخدم
├── 📁 Forms/                       # النوافذ الرئيسية
│   ├── WelcomeForm.cs              # شاشة الترحيب
│   ├── LoginForm.cs                # شاشة تسجيل الدخول
│   ├── SignUpForm.cs               # شاشة إنشاء حساب
│   ├── MainForm.cs                 # الشاشة الرئيسية
│   ├── RecordRunForm.cs            # شاشة تسجيل رحلة
│   └── RunDetailsForm.cs           # شاشة تفاصيل الرحلة
└── 📁 Views/                       # واجهات المستخدم
    ├── HomeView.cs                 # واجهة الصفحة الرئيسية
    ├── ExploreView.cs              # واجهة استكشاف المنتجعات
    ├── RecordView.cs               # واجهة سجل الرحلات
    └── ProfileView.cs              # واجهة الملف الشخصي

🔧 التقنيات المستخدمة:

1. إطار العمل:
   - .NET 8.0
   - Windows Forms
   - C# 12.0

2. قاعدة البيانات:
   - SQLite (System.Data.SQLite)
   - قاعدة بيانات محلية
   - جداول: Users, Resorts, SkiRuns

3. الأمان:
   - تشفير SHA256 لكلمات المرور
   - التحقق من صحة البيانات
   - حماية من SQL Injection

4. واجهة المستخدم:
   - Windows Forms Controls
   - تصميم مخصص يحاكي Tailwind CSS
   - ألوان وخطوط متناسقة

5. إدارة البيانات:
   - Repository Pattern
   - CRUD Operations
   - Data Validation

📊 قاعدة البيانات:

1. جدول Users:
   - Id (Primary Key)
   - Username, Email, PasswordHash
   - FirstName, LastName
   - ProfileImagePath
   - CreatedAt, LastLoginAt

2. جدول Resorts:
   - Id (Primary Key)
   - Name, Location, State, Country
   - TotalAcres, TotalTrails
   - ImageUrl, Description
   - IsPopular

3. جدول SkiRuns:
   - Id (Primary Key)
   - UserId (Foreign Key)
   - ResortId (Foreign Key)
   - Date, Distance, Duration
   - AverageSpeed, MaxSpeed
   - ElevationChange
   - Notes, ImagePath

🎨 واجهة المستخدم:

1. الألوان الرئيسية:
   - أزرق: #327FCC (الأزرار النشطة)
   - رمادي: #E9EDF1 (الخلفية)
   - أبيض: #FFFFFF (البطاقات)
   - نص: #374151 (النص الرئيسي)

2. التخطيط:
   - تصميم مستجيب
   - تنقل سفلي
   - بطاقات للمحتوى
   - أيقونات واضحة

3. التفاعل:
   - أزرار تفاعلية
   - رسائل تأكيد
   - تحديث فوري للبيانات
   - تنقل سلس بين الشاشات

🔄 تدفق العمل:

1. بدء التطبيق → شاشة الترحيب
2. تسجيل دخول/إنشاء حساب
3. الشاشة الرئيسية مع التنقل
4. استكشاف المنتجعات
5. تسجيل رحلات جديدة
6. عرض الإحصائيات والتفاصيل

===============================================
