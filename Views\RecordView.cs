using SkiTrackApp.Data;
using SkiTrackApp.Services;

namespace SkiTrackApp.Forms
{
    public partial class RecordView : UserControl
    {
        private readonly SkiRunRepository skiRunRepository;
        private Panel? runsPanel;
        
        public RecordView()
        {
            InitializeComponent();
            skiRunRepository = new SkiRunRepository();
            SetupView();
            LoadRuns();
        }
        
        private void SetupView()
        {
            this.BackColor = Color.FromArgb(249, 250, 251);
            this.Dock = DockStyle.Fill;
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            
            // Header
            var headerLabel = new Label
            {
                Text = "My Runs",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Add new run button
            var addRunButton = new Button
            {
                Text = "+ Record New Run",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(400, 45),
                Location = new Point(20, 70),
                BackColor = Color.FromArgb(50, 127, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            addRunButton.FlatAppearance.BorderSize = 0;
            addRunButton.Click += AddRunButton_Click;
            
            // Add hover effect
            addRunButton.MouseEnter += (s, e) => addRunButton.BackColor = Color.FromArgb(37, 99, 235);
            addRunButton.MouseLeave += (s, e) => addRunButton.BackColor = Color.FromArgb(50, 127, 204);
            
            // Runs title
            var runsTitle = new Label
            {
                Text = "All Runs",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(400, 30),
                Location = new Point(20, 140),
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            // Runs panel
            runsPanel = new Panel
            {
                Size = new Size(400, 400),
                Location = new Point(20, 180),
                BackColor = Color.Transparent,
                AutoScroll = true
            };
            
            mainPanel.Controls.AddRange(new Control[] 
            { 
                headerLabel, addRunButton, runsTitle, runsPanel 
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void LoadRuns()
        {
            if (UserSession.CurrentUser == null || runsPanel == null) return;
            
            var runs = skiRunRepository.GetUserRuns(UserSession.CurrentUser.Id);
            
            runsPanel.Controls.Clear();
            
            if (!runs.Any())
            {
                var noRunsLabel = new Label
                {
                    Text = "No runs recorded yet.\nClick 'Record New Run' to get started!",
                    Font = new Font("Segoe UI", 11),
                    ForeColor = Color.FromArgb(107, 114, 128),
                    Size = new Size(380, 80),
                    Location = new Point(10, 100),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                runsPanel.Controls.Add(noRunsLabel);
                return;
            }
            
            int yPos = 0;
            foreach (var run in runs)
            {
                var runPanel = CreateRunCard(run, yPos);
                runsPanel.Controls.Add(runPanel);
                yPos += 90;
            }
        }
        
        private Panel CreateRunCard(Models.SkiRun run, int yPosition)
        {
            var panel = new Panel
            {
                Size = new Size(380, 80),
                Location = new Point(0, yPosition),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Cursor = Cursors.Hand
            };
            
            // Icon
            var iconLabel = new Label
            {
                Text = "🎿",
                Font = new Font("Segoe UI", 20),
                Size = new Size(50, 50),
                Location = new Point(15, 15),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(233, 237, 241)
            };
            
            // Date and time
            var dateLabel = new Label
            {
                Text = run.FormattedDate,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(150, 20),
                Location = new Point(75, 15)
            };
            
            // Resort
            var resortLabel = new Label
            {
                Text = run.Resort?.Name ?? "Unknown Resort",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(150, 20),
                Location = new Point(75, 35)
            };
            
            // Duration
            var durationLabel = new Label
            {
                Text = run.FormattedDuration,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(150, 20),
                Location = new Point(75, 55)
            };
            
            // Distance
            var distanceLabel = new Label
            {
                Text = $"{run.Distance:F1} km",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(80, 25),
                Location = new Point(280, 15),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            // Average speed
            var speedLabel = new Label
            {
                Text = $"{run.AverageSpeed:F1} km/h",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(80, 20),
                Location = new Point(280, 40),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            panel.Controls.AddRange(new Control[] 
            { 
                iconLabel, dateLabel, resortLabel, durationLabel, distanceLabel, speedLabel 
            });
            
            // Click event to show run details
            panel.Click += (s, e) => ShowRunDetails(run.Id);
            
            return panel;
        }
        
        private void AddRunButton_Click(object? sender, EventArgs e)
        {
            var recordForm = new RecordRunForm();
            if (recordForm.ShowDialog() == DialogResult.OK)
            {
                LoadRuns(); // Refresh the list
            }
        }
        
        private void ShowRunDetails(int runId)
        {
            var runDetailsForm = new RunDetailsForm(runId);
            runDetailsForm.ShowDialog();
        }
    }
}
