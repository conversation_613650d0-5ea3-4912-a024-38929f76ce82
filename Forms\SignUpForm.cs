using SkiTrackApp.Data;

namespace SkiTrackApp.Forms
{
    public partial class SignUpForm : Form
    {
        private TextBox? usernameTextBox;
        private TextBox? emailTextBox;
        private TextBox? passwordTextBox;
        private TextBox? confirmPasswordTextBox;
        private TextBox? firstNameTextBox;
        private TextBox? lastNameTextBox;
        private Button? signUpButton;
        private Button? backToLoginButton;
        private readonly UserRepository userRepository;
        
        public SignUpForm()
        {
            InitializeComponent();
            userRepository = new UserRepository();
            SetupForm();
        }
        
        private void SetupForm()
        {
            this.Text = "SkiTrack - Sign Up";
            this.Size = new Size(400, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(249, 250, 251);
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                AutoScroll = true
            };
            
            // Title
            var titleLabel = new Label
            {
                Text = "Create Account",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(360, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            int yPos = 80;
            
            // First Name
            var firstNameLabel = new Label
            {
                Text = "First Name",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(170, 20),
                Location = new Point(20, yPos)
            };
            
            firstNameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(170, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Last Name
            var lastNameLabel = new Label
            {
                Text = "Last Name",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(170, 20),
                Location = new Point(210, yPos)
            };
            
            lastNameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(170, 35),
                Location = new Point(210, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            yPos += 80;
            
            // Username
            var usernameLabel = new Label
            {
                Text = "Username",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, yPos)
            };
            
            usernameTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(360, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            yPos += 80;
            
            // Email
            var emailLabel = new Label
            {
                Text = "Email",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, yPos)
            };
            
            emailTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(360, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            yPos += 80;
            
            // Password
            var passwordLabel = new Label
            {
                Text = "Password",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, yPos)
            };
            
            passwordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(360, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true
            };
            
            yPos += 80;
            
            // Confirm Password
            var confirmPasswordLabel = new Label
            {
                Text = "Confirm Password",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(87, 115, 142),
                Size = new Size(360, 20),
                Location = new Point(20, yPos)
            };
            
            confirmPasswordTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(360, 35),
                Location = new Point(20, yPos + 25),
                BackColor = Color.FromArgb(233, 237, 241),
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true
            };
            
            yPos += 80;
            
            // Sign Up button
            signUpButton = new Button
            {
                Text = "Sign Up",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(360, 45),
                Location = new Point(20, yPos),
                BackColor = Color.FromArgb(50, 127, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            signUpButton.FlatAppearance.BorderSize = 0;
            signUpButton.Click += SignUpButton_Click;
            
            yPos += 60;
            
            // Back to Login button
            backToLoginButton = new Button
            {
                Text = "Back to Login",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(360, 45),
                Location = new Point(20, yPos),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(16, 20, 25),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            backToLoginButton.FlatAppearance.BorderSize = 0;
            backToLoginButton.Click += BackToLoginButton_Click;
            
            // Add hover effects
            signUpButton.MouseEnter += (s, e) => signUpButton.BackColor = Color.FromArgb(37, 99, 235);
            signUpButton.MouseLeave += (s, e) => signUpButton.BackColor = Color.FromArgb(50, 127, 204);
            
            backToLoginButton.MouseEnter += (s, e) => backToLoginButton.BackColor = Color.FromArgb(209, 213, 219);
            backToLoginButton.MouseLeave += (s, e) => backToLoginButton.BackColor = Color.FromArgb(233, 237, 241);
            
            // Add controls to main panel
            mainPanel.Controls.AddRange(new Control[] 
            { 
                titleLabel, firstNameLabel, firstNameTextBox, lastNameLabel, lastNameTextBox,
                usernameLabel, usernameTextBox, emailLabel, emailTextBox, 
                passwordLabel, passwordTextBox, confirmPasswordLabel, confirmPasswordTextBox,
                signUpButton, backToLoginButton
            });
            
            this.Controls.Add(mainPanel);
        }
        
        private void SignUpButton_Click(object? sender, EventArgs e)
        {
            // Validation
            if (string.IsNullOrWhiteSpace(usernameTextBox?.Text) ||
                string.IsNullOrWhiteSpace(emailTextBox?.Text) ||
                string.IsNullOrWhiteSpace(passwordTextBox?.Text) ||
                string.IsNullOrWhiteSpace(confirmPasswordTextBox?.Text))
            {
                MessageBox.Show("Please fill in all required fields.", "Sign Up Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            if (passwordTextBox.Text != confirmPasswordTextBox.Text)
            {
                MessageBox.Show("Passwords do not match.", "Sign Up Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            if (passwordTextBox.Text.Length < 6)
            {
                MessageBox.Show("Password must be at least 6 characters long.", "Sign Up Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            // Create user
            var success = userRepository.CreateUser(
                usernameTextBox.Text,
                emailTextBox.Text,
                passwordTextBox.Text,
                firstNameTextBox?.Text ?? "",
                lastNameTextBox?.Text ?? ""
            );
            
            if (success)
            {
                MessageBox.Show("Account created successfully! Please log in.", "Success", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                var loginForm = new LoginForm();
                loginForm.Show();
                this.Hide();
            }
            else
            {
                MessageBox.Show("Username or email already exists.", "Sign Up Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BackToLoginButton_Click(object? sender, EventArgs e)
        {
            var loginForm = new LoginForm();
            loginForm.Show();
            this.Hide();
        }
    }
}
