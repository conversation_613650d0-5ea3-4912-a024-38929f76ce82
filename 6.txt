<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-gray-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-gray-50 p-4 pb-2 justify-between">
          <div class="text-[#101419] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Settings</h2>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Profile</h3>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-fit"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB7ZLHELV1CzWgMTGuIHsiq4jBdVFHmHffRQLU1WEI3SLQ1dImtOYppEWmxhsciteA5acT1SolEE7-y5S7Fw97W0TAt7RQ4_aXYmcXS4gFCK9vSN-ieWd05LMtcAP3FPsps6zabL5tCEmUpaRs8Jzo-J1JMSW21V9zMzYHORzQN6xKdCNI9zpuX877JgEcVuUhsxs7Bqi6h5v9QzK1Z4dm6lb76Tt_Al6zrCV_MOw1mWV1PcuWRNI5u2_6I2sQ4-C8KZreAIvWMqxs");'
          ></div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">Profile</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Edit your profile</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="User" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">Account</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Manage your account</p>
          </div>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Preferences</h3>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="Gear" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">App Preferences</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Customize your app experience</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="Bell" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">Notifications</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Manage your notifications</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="Shield" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M208,40H48A16,16,0,0,0,32,56v58.77c0,89.61,75.82,119.34,91,124.39a15.53,15.53,0,0,0,10,0c15.2-5.05,91-34.78,91-124.39V56A16,16,0,0,0,208,40Zm0,74.79c0,78.42-66.35,104.62-80,109.18-13.53-4.51-80-30.69-80-109.18V56l160,0Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">Privacy</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Adjust your privacy settings</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="Link" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M137.54,186.36a8,8,0,0,1,0,11.31l-9.94,10A56,56,0,0,1,48.38,128.4L72.5,104.28A56,56,0,0,1,149.31,102a8,8,0,1,1-10.64,12,40,40,0,0,0-54.85,1.63L59.7,139.72a40,40,0,0,0,56.58,56.58l9.94-9.94A8,8,0,0,1,137.54,186.36Zm70.08-138a56.08,56.08,0,0,0-79.22,0l-9.94,9.95a8,8,0,0,0,11.32,11.31l9.94-9.94a40,40,0,0,1,56.58,56.58L172.18,140.4A40,40,0,0,1,117.33,142,8,8,0,1,0,106.69,154a56,56,0,0,0,76.81-2.26l24.12-24.12A56.08,56.08,0,0,0,207.62,48.38Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">Connected Accounts</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Connect to other services</p>
          </div>
        </div>
        <h3 class="text-[#101419] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Support</h3>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="Question" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">Help &amp; Support</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Get help and support</p>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-gray-50 px-4 min-h-[72px] py-2">
          <div class="text-[#101419] flex items-center justify-center rounded-lg bg-[#e9edf1] shrink-0 size-12" data-icon="Info" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm16-40a8,8,0,0,1-8,8,16,16,0,0,1-16-16V128a8,8,0,0,1,0-16,16,16,0,0,1,16,16v40A8,8,0,0,1,144,176ZM112,84a12,12,0,1,1,12,12A12,12,0,0,1,112,84Z"
              ></path>
            </svg>
          </div>
          <div class="flex flex-col justify-center">
            <p class="text-[#101419] text-base font-medium leading-normal line-clamp-1">About</p>
            <p class="text-[#57738e] text-sm font-normal leading-normal line-clamp-2">Learn more about the app</p>
          </div>
        </div>
      </div>
      <div>
        <div class="flex gap-2 border-t border-[#e9edf1] bg-gray-50 px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#57738e] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
              </svg>
            </div>
            <p class="text-[#57738e] text-xs font-medium leading-normal tracking-[0.015em]">Explore</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#57738e]" href="#">
            <div class="text-[#57738e] flex h-8 items-center justify-center" data-icon="RewindCircle" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216ZM179.77,89a8,8,0,0,0-8.21.39l-48,32a8,8,0,0,0,0,13.32l48,32A8,8,0,0,0,176,168a8,8,0,0,0,8-8V96A8,8,0,0,0,179.77,89ZM168,145.05,142.42,128,168,111ZM115.77,89a8,8,0,0,0-8.21.39l-48,32a8,8,0,0,0,0,13.32l48,32A8,8,0,0,0,112,168a8,8,0,0,0,8-8V96A8,8,0,0,0,115.77,89ZM104,145.05,78.42,128,104,111Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#57738e] text-xs font-medium leading-normal tracking-[0.015em]">Record</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#101419]" href="#">
            <div class="text-[#101419] flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M230.93,220a8,8,0,0,1-6.93,4H32a8,8,0,0,1-6.92-12c15.23-26.33,38.7-45.21,66.09-54.16a72,72,0,1,1,73.66,0c27.39,8.95,50.86,27.83,66.09,54.16A8,8,0,0,1,230.93,220Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#101419] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </a>
        </div>
        <div class="h-5 bg-gray-50"></div>
      </div>
    </div>
  </body>
</html>
