===============================================
           دليل المستخدم - تطبيق SkiTrack
===============================================

🚀 كيفية تشغيل التطبيق:

1. متطلبات النظام:
   - Windows 10 أو أحدث
   - .NET 8.0 Runtime
   - 50 ميجابايت مساحة فارغة

2. تشغيل التطبيق:
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد المشروع
   - اكتب: dotnet run
   - أو: dotnet build ثم تشغيل الملف التنفيذي

===============================================

📱 كيفية استخدام التطبيق:

المرحلة 1: البداية
====================
1. عند تشغيل التطبيق، ستظهر شاشة الترحيب
2. اختر "تسجيل الدخول" إذا كان لديك حساب
3. أو اختر "إنشاء حساب جديد" للمستخدمين الجدد

المرحلة 2: إنشاء حساب جديد
============================
1. أدخل اسم المستخدم (يجب أن يكون فريداً)
2. أدخل البريد الإلكتروني
3. اختر كلمة مرور قوية
4. أدخل الاسم الأول والأخير (اختياري)
5. اضغط "إنشاء حساب"

المرحلة 3: تسجيل الدخول
=========================
1. أدخل اسم المستخدم أو البريد الإلكتروني
2. أدخل كلمة المرور
3. اضغط "تسجيل الدخول"

المرحلة 4: الشاشة الرئيسية
============================
بعد تسجيل الدخول، ستجد 4 أقسام رئيسية:

🏠 الرئيسية (Home):
- ترحيب شخصي بالمستخدم
- عرض الإحصائيات السريعة
- آخر الرحلات المسجلة
- نظرة عامة على النشاط

🔍 استكشاف (Explore):
- قائمة بجميع منتجعات التزلج
- معلومات تفصيلية لكل منتجع:
  * الاسم والموقع
  * عدد المسارات والمساحة
  * وصف المنتجع
- إمكانية البحث عن منتجع معين
- فلترة المنتجعات الشائعة

📊 السجل (Record):
- قائمة بجميع رحلاتك المسجلة
- ترتيب حسب التاريخ (الأحدث أولاً)
- عرض معلومات سريعة لكل رحلة:
  * التاريخ والمنتجع
  * المسافة والمدة
  * السرعة المتوسطة
- إمكانية عرض تفاصيل أي رحلة

👤 الملف الشخصي (Profile):
- معلوماتك الشخصية
- الإحصائيات الشاملة:
  * إجمالي الرحلات
  * إجمالي المسافة
  * إجمالي الوقت
  * متوسط السرعة
  * أقصى سرعة
  * إجمالي الارتفاع

===============================================

🎿 كيفية تسجيل رحلة جديدة:

1. من أي شاشة، اضغط على زر "+" في الأسفل
2. ستفتح شاشة "تسجيل رحلة جديدة"
3. املأ المعلومات المطلوبة:

📅 التاريخ:
- اختر تاريخ الرحلة
- افتراضياً: اليوم الحالي

🏔️ المنتجع:
- اختر المنتجع من القائمة المنسدلة
- جميع المنتجعات متاحة

📏 المسافة:
- أدخل المسافة بالأميال
- مثال: 5.2

⏱️ المدة:
- أدخل مدة الرحلة
- تنسيق: ساعات:دقائق:ثواني
- مثال: 02:30:00 (ساعتان ونصف)

🏃 السرعة المتوسطة:
- أدخل السرعة المتوسطة بالميل/ساعة
- مثال: 15.5

⚡ أقصى سرعة:
- أدخل أقصى سرعة وصلت إليها
- مثال: 25.8

⛰️ تغيير الارتفاع:
- أدخل إجمالي تغيير الارتفاع بالقدم
- مثال: 1200

📝 ملاحظات (اختياري):
- أضف أي ملاحظات عن الرحلة
- حالة الطقس، صعوبة المسارات، إلخ

4. اضغط "حفظ الرحلة"

===============================================

📈 عرض تفاصيل الرحلة:

1. من شاشة السجل، اضغط على أي رحلة
2. ستفتح شاشة تفاصيل الرحلة مع:

📊 معلومات أساسية:
- التاريخ والوقت
- اسم المنتجع وموقعه
- المسافة والمدة
- السرعات والارتفاع

📈 إحصائيات مفصلة:
- السرعة المتوسطة مقابل الأقصى
- معدل تغيير الارتفاع
- مقارنة مع رحلاتك الأخرى

📝 الملاحظات:
- عرض الملاحظات المحفوظة
- إمكانية التعديل (في الإصدارات المستقبلية)

===============================================

🔧 نصائح للاستخدام الأمثل:

1. دقة البيانات:
   - تأكد من دقة المسافة والوقت
   - استخدم تطبيقات GPS للقياس الدقيق
   - سجل الرحلة فور انتهائها

2. الملاحظات المفيدة:
   - اكتب حالة الطقس
   - صعوبة المسارات
   - نوع الثلج
   - المعدات المستخدمة

3. تتبع التقدم:
   - راجع الإحصائيات بانتظام
   - قارن أداءك عبر الوقت
   - حدد أهدافاً للتحسن

4. أمان البيانات:
   - البيانات محفوظة محلياً
   - لا تحتاج اتصال إنترنت
   - انسخ ملف skitrack.db احتياطياً

===============================================

❓ حل المشاكل الشائعة:

1. التطبيق لا يبدأ:
   - تأكد من تثبيت .NET 8.0
   - تحقق من صلاحيات المجلد
   - شغل كـ Administrator

2. لا يمكن تسجيل الدخول:
   - تأكد من صحة اسم المستخدم
   - تأكد من صحة كلمة المرور
   - جرب إنشاء حساب جديد

3. البيانات لا تحفظ:
   - تأكد من صلاحيات الكتابة
   - تحقق من مساحة القرص
   - أعد تشغيل التطبيق

4. الواجهة لا تظهر بشكل صحيح:
   - تأكد من دقة الشاشة
   - جرب تغيير حجم النافذة
   - أعد تشغيل التطبيق

===============================================

🎉 استمتع بتتبع رحلات التزلج مع SkiTrack!

===============================================
