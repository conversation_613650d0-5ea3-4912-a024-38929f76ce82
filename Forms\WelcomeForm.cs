using SkiTrackApp.Forms;

namespace SkiTrackApp.Forms
{
    public partial class WelcomeForm : Form
    {
        public WelcomeForm()
        {
            InitializeComponent();
            SetupForm();
        }
        
        private void SetupForm()
        {
            this.Text = "SkiTrack - Track your ski adventures";
            this.Size = new Size(400, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(249, 250, 251); // bg-gray-50
            
            CreateControls();
        }
        
        private void CreateControls()
        {
            // Main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };
            
            // Hero image placeholder
            var heroPanel = new Panel
            {
                Size = new Size(360, 200),
                Location = new Point(20, 50),
                BackColor = Color.FromArgb(229, 231, 235),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            var heroLabel = new Label
            {
                Text = "🎿 Ski Adventure Image",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(107, 114, 128),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            heroPanel.Controls.Add(heroLabel);
            
            // Title
            var titleLabel = new Label
            {
                Text = "Track your ski adventures",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(360, 40),
                Location = new Point(20, 270),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Description
            var descLabel = new Label
            {
                Text = "Record your runs, explore resorts, and share your experiences with friends.",
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(16, 20, 25),
                Size = new Size(360, 60),
                Location = new Point(20, 320),
                TextAlign = ContentAlignment.TopCenter
            };
            
            // Get Started button
            var getStartedBtn = new Button
            {
                Text = "Get Started",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(360, 50),
                Location = new Point(20, 420),
                BackColor = Color.FromArgb(50, 127, 204), // bg-blue-600
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            getStartedBtn.FlatAppearance.BorderSize = 0;
            getStartedBtn.Click += GetStartedBtn_Click;
            
            // Add hover effect
            getStartedBtn.MouseEnter += (s, e) => getStartedBtn.BackColor = Color.FromArgb(37, 99, 235);
            getStartedBtn.MouseLeave += (s, e) => getStartedBtn.BackColor = Color.FromArgb(50, 127, 204);
            
            // Add controls to main panel
            mainPanel.Controls.AddRange(new Control[] { heroPanel, titleLabel, descLabel, getStartedBtn });
            
            this.Controls.Add(mainPanel);
        }
        
        private void GetStartedBtn_Click(object? sender, EventArgs e)
        {
            var loginForm = new LoginForm();
            loginForm.Show();
            this.Hide();
        }
    }
}
