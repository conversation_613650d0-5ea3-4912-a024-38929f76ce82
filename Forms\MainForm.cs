using SkiTrackApp.Services;
using SkiTrackApp.Data;

namespace SkiTrackApp.Forms
{
    public partial class MainForm : Form
    {
        private Panel? contentPanel;
        private Panel? navigationPanel;
        private Button? homeButton;
        private Button? exploreButton;
        private Button? recordButton;
        private Button? profileButton;
        
        private UserControl? currentView;
        
        public MainForm()
        {
            InitializeComponent();
            SetupForm();
            ShowHomeView();
        }
        
        private void SetupForm()
        {
            this.Text = "SkiTrack";
            this.Size = new Size(450, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(249, 250, 251);
            
            CreateLayout();
        }
        
        private void CreateLayout()
        {
            // Content panel (main area)
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(249, 250, 251)
            };
            
            // Navigation panel (bottom)
            navigationPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = Color.FromArgb(249, 250, 251),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            CreateNavigationButtons();
            
            this.Controls.Add(contentPanel);
            this.Controls.Add(navigationPanel);
        }
        
        private void CreateNavigationButtons()
        {
            var buttonWidth = 100;
            var buttonHeight = 60;
            var spacing = 10;
            
            // Home button
            homeButton = new Button
            {
                Text = "🏠\nHome",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(spacing, 10),
                BackColor = Color.FromArgb(50, 127, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                TextAlign = ContentAlignment.MiddleCenter
            };
            homeButton.FlatAppearance.BorderSize = 0;
            homeButton.Click += (s, e) => ShowHomeView();
            
            // Explore button
            exploreButton = new Button
            {
                Text = "🔍\nExplore",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(spacing + buttonWidth + spacing, 10),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(87, 115, 142),
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                TextAlign = ContentAlignment.MiddleCenter
            };
            exploreButton.FlatAppearance.BorderSize = 0;
            exploreButton.Click += (s, e) => ShowExploreView();
            
            // Record button
            recordButton = new Button
            {
                Text = "📊\nRecord",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(spacing + (buttonWidth + spacing) * 2, 10),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(87, 115, 142),
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                TextAlign = ContentAlignment.MiddleCenter
            };
            recordButton.FlatAppearance.BorderSize = 0;
            recordButton.Click += (s, e) => ShowRecordView();
            
            // Profile button
            profileButton = new Button
            {
                Text = "👤\nProfile",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(spacing + (buttonWidth + spacing) * 3, 10),
                BackColor = Color.FromArgb(233, 237, 241),
                ForeColor = Color.FromArgb(87, 115, 142),
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9),
                TextAlign = ContentAlignment.MiddleCenter
            };
            profileButton.FlatAppearance.BorderSize = 0;
            profileButton.Click += (s, e) => ShowProfileView();
            
            navigationPanel?.Controls.AddRange(new Control[] { homeButton, exploreButton, recordButton, profileButton });
        }
        
        private void UpdateNavigationButtons(Button activeButton)
        {
            var buttons = new[] { homeButton, exploreButton, recordButton, profileButton };
            
            foreach (var button in buttons)
            {
                if (button != null)
                {
                    if (button == activeButton)
                    {
                        button.BackColor = Color.FromArgb(50, 127, 204);
                        button.ForeColor = Color.White;
                    }
                    else
                    {
                        button.BackColor = Color.FromArgb(233, 237, 241);
                        button.ForeColor = Color.FromArgb(87, 115, 142);
                    }
                }
            }
        }
        
        private void ShowView(UserControl view, Button activeButton)
        {
            currentView?.Dispose();
            currentView = view;
            view.Dock = DockStyle.Fill;
            contentPanel?.Controls.Clear();
            contentPanel?.Controls.Add(view);
            UpdateNavigationButtons(activeButton);
        }
        
        private void ShowHomeView()
        {
            ShowView(new HomeView(), homeButton!);
        }
        
        private void ShowExploreView()
        {
            ShowView(new ExploreView(), exploreButton!);
        }
        
        private void ShowRecordView()
        {
            ShowView(new RecordView(), recordButton!);
        }
        
        private void ShowProfileView()
        {
            ShowView(new ProfileView(), profileButton!);
        }
        
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            Application.Exit();
            base.OnFormClosing(e);
        }
    }
}
