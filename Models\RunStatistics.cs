namespace SkiTrackApp.Models
{
    public class RunStatistics
    {
        public double TotalDistance { get; set; }
        public double AverageSpeed { get; set; }
        public TimeSpan TotalTime { get; set; }
        public int TotalRuns { get; set; }
        public double MaxSpeed { get; set; }
        public double TotalElevation { get; set; }
        
        public string FormattedTotalTime => $"{TotalTime.TotalHours:F0} hours";
        public string FormattedTotalDistance => $"{TotalDistance:F1} km";
        public string FormattedAverageSpeed => $"{AverageSpeed:F0} km/h";
    }
}
